<template>
  <div>
    <div v-if="pending" class="space-y-4">
      <div class="skeleton h-8 w-3/4"></div>
      <div class="skeleton h-4 w-1/2"></div>
      <div class="skeleton h-64 w-full"></div>
    </div>
    
    <div v-else-if="error" class="alert alert-error">
      <span>笔记加载失败</span>
    </div>
    
    <article v-else class="prose prose-lg max-w-none">
      <header class="mb-8">
        <h1 class="text-4xl font-bold mb-4">{{ data?.title }}</h1>
        <div class="flex items-center gap-4 text-base-content/60">
          <time>{{ formatDate(data?.created) }}</time>
          <span v-if="data?.updated !== data?.created">
            更新于 {{ formatDate(data?.updated) }}
          </span>
        </div>
      </header>
      
      <div class="divider"></div>
      
      <div class="prose-content" v-html="renderedContent"></div>
    </article>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const nid = route.params.nid

// Page meta
definePageMeta({
  validate: async (route) => {
    return /^\d+$/.test(route.params.nid as string)
  }
})

// Data fetching
const { data, pending, error } = await useFetch(`/api/notes/${nid}`)

// SEO
useHead({
  title: () => data.value?.title || '笔记详情',
  meta: [
    { name: 'description', content: () => data.value?.summary || '' },
    { property: 'og:title', content: () => data.value?.title || '' },
    { property: 'og:description', content: () => data.value?.summary || '' },
    { property: 'og:type', content: 'article' }
  ]
})

// Render markdown content (simplified)
const renderedContent = computed(() => {
  if (!data.value?.content) return ''
  
  // Simple markdown to HTML conversion
  return data.value.content
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/\n/gim, '<br>')
})

// Utility function
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<style scoped>
.prose-content {
  @apply text-base-content leading-relaxed;
}

.prose-content h1,
.prose-content h2,
.prose-content h3 {
  @apply font-bold text-base-content mt-8 mb-4;
}

.prose-content h1 {
  @apply text-2xl;
}

.prose-content h2 {
  @apply text-xl;
}

.prose-content h3 {
  @apply text-lg;
}

.prose-content p {
  @apply mb-4;
}

.prose-content strong {
  @apply font-semibold;
}

.prose-content em {
  @apply italic;
}
</style>
