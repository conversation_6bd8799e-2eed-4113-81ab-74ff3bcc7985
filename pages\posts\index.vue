<template>
  <div>
    <h1 class="text-3xl font-bold mb-8">文章列表</h1>
    
    <div v-if="pending" class="space-y-4">
      <div v-for="i in 5" :key="i" class="skeleton h-32 w-full"></div>
    </div>
    
    <div v-else-if="error" class="alert alert-error">
      <span>加载文章失败</span>
    </div>
    
    <div v-else class="space-y-6">
      <article 
        v-for="post in data?.data" 
        :key="post.id"
        class="card bg-base-100 shadow-xl"
      >
        <div class="card-body">
          <h2 class="card-title">
            <NuxtLink 
              :to="`/posts/${post.category.slug}/${post.slug}`"
              class="hover:text-primary transition-colors"
            >
              {{ post.title }}
            </NuxtLink>
          </h2>
          <p class="text-base-content/70">{{ post.summary }}</p>
          <div class="card-actions justify-end">
            <div class="badge badge-outline">{{ post.category.name }}</div>
            <div class="text-sm text-base-content/50">
              {{ formatDate(post.created) }}
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</template>

<script setup lang="ts">
// Page meta
definePageMeta({
  title: '文章'
})

// SEO
useHead({
  title: '文章列表',
  meta: [
    { name: 'description', content: '查看所有文章' }
  ]
})

// Data fetching
const { data, pending, error } = await useFetch('/api/posts')

// Utility function
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>
