{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "skipLibCheck": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/.pnpm/nitropack@2.11.13_@upstash+redis@1.34.9_idb-keyval@6.2.2/node_modules/nitropack/types"], "nitropack": ["../node_modules/.pnpm/nitropack@2.11.13_@upstash+redis@1.34.9_idb-keyval@6.2.2/node_modules/nitropack"], "defu": ["../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../node_modules/.pnpm/h3@1.15.3/node_modules/h3"], "consola": ["../node_modules/.pnpm/consola@3.4.2/node_modules/consola"], "@unhead/vue": ["../node_modules/.pnpm/@unhead+vue@1.11.20_vue@3.5.13_typescript@5.8.3_/node_modules/@unhead/vue"], "@vue/runtime-core": ["../node_modules/.pnpm/@vue+runtime-core@3.5.13/node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/.pnpm/@vue+compiler-sfc@3.5.13/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/.pnpm/unplugin-vue-router@0.10.9__c5e6035d13af0ea6b4a8335ad17ca1c5/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/.pnpm/@nuxt+schema@3.14.159_magicast@0.3.5_rollup@4.44.1/node_modules/@nuxt/schema"], "nuxt": ["../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_91dbb18be3a1b09f523d435cda265240/node_modules/nuxt"], "~": ["./.."], "@": ["./.."], "~~": ["./.."], "@@": ["./.."], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_91dbb18be3a1b09f523d435cda265240/node_modules/nuxt/dist/core/runtime/nitro/paths"], "#color-mode-options": ["./color-mode-options"], "pinia": ["../node_modules/.pnpm/pinia@2.2.8_typescript@5.8.3_vue@3.5.13_typescript@5.8.3_/node_modules/pinia/dist/pinia"], "#vue-router": ["./vue-router"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/.pnpm/@nuxtjs+tailwindcss@6.12.2_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/.pnpm/@nuxtjs+color-mode@3.5.1_magicast@0.3.5/node_modules/@nuxtjs/color-mode/runtime/server", "../node_modules/.pnpm/@pinia+nuxt@0.5.5_magicast@_2f5ed5d60aba0305c1485f3d37926370/node_modules/@pinia/nuxt/runtime/server", "../node_modules/.pnpm/@vueuse+nuxt@11.3.0_magicas_cd2d7689b8c7f9898f928ebe63561c88/node_modules/@vueuse/nuxt/runtime/server", "../node_modules/.pnpm/@nuxt+devtools@1.6.0_buffer_39363466d61e363c3004192fa43c62d0/node_modules/@nuxt/devtools/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_91dbb18be3a1b09f523d435cda265240/node_modules/nuxt/node_modules", "../dist"]}