@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  font-size: 14px;
  line-height: 1.5;
}

html body {
  @apply max-w-screen overflow-x-hidden;
}

@media print {
  html {
    font-size: 12px;
  }
}

/* @media (min-width: 2160px) {
  html {
    font-size: 15px;
  }
} */

.prose {
  max-width: 100% !important;
  font-size: 1.1rem;

  p {
    @apply break-words;
  }

  figure img {
    @apply mb-0 mt-0;
  }
}

*:focus {
  outline: none;
}

*:not(input):not(textarea):not([contenteditable='true']):focus-visible {
  /* outline: 0 !important; */
  /* box-shadow: theme(colors.accent) 0px 0px 0px 1px; */
}
* {
  tab-size: 2;

  @apply border-border;
  &:hover {
    scrollbar-color: auto;
  }
}

.animate-ping {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%,
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* input,
textarea {
  font-size: max(16px, 1rem);
} */

a {
  @apply break-all;
}

@screen lg {
  input,
  textarea {
    font-size: 1rem;
  }
}

.prose p:last-child {
  margin-bottom: 0;
}

.prose
  :where(blockquote):not(:where([class~='not-prose'], [class~='not-prose'] *)) {
  @apply relative border-0;

  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 1em;
    background-color: theme(colors.accent);
  }
}
