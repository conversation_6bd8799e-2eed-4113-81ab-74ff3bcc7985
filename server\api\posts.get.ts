export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { size = 10, page = 1 } = query

  // Mock data - replace with real API calls
  const mockPosts = [
    {
      id: '1',
      title: '欢迎使用 Nuxt.js',
      slug: 'welcome-to-nuxtjs',
      summary: '这是一篇关于 Nuxt.js 的介绍文章',
      content: '# 欢迎使用 Nuxt.js\n\nNuxt.js 是一个基于 Vue.js 的框架...',
      created: '2024-01-01T00:00:00Z',
      updated: '2024-01-01T00:00:00Z',
      category: {
        id: '1',
        name: '技术',
        slug: 'tech'
      }
    },
    {
      id: '2',
      title: 'TypeScript 最佳实践',
      slug: 'typescript-best-practices',
      summary: '分享一些 TypeScript 开发的最佳实践',
      content: '# TypeScript 最佳实践\n\n在现代前端开发中...',
      created: '2024-01-02T00:00:00Z',
      updated: '2024-01-02T00:00:00Z',
      category: {
        id: '1',
        name: '技术',
        slug: 'tech'
      }
    },
    {
      id: '3',
      title: 'Tailwind CSS 使用指南',
      slug: 'tailwind-css-guide',
      summary: '如何高效使用 Tailwind CSS 进行样式开发',
      content: '# Tailwind CSS 使用指南\n\nTailwind CSS 是一个...',
      created: '2024-01-03T00:00:00Z',
      updated: '2024-01-03T00:00:00Z',
      category: {
        id: '1',
        name: '技术',
        slug: 'tech'
      }
    }
  ]

  // Simulate pagination
  const startIndex = (Number(page) - 1) * Number(size)
  const endIndex = startIndex + Number(size)
  const paginatedPosts = mockPosts.slice(startIndex, endIndex)

  return {
    data: paginatedPosts,
    pagination: {
      currentPage: Number(page),
      totalPages: Math.ceil(mockPosts.length / Number(size)),
      totalItems: mockPosts.length,
      hasNextPage: endIndex < mockPosts.length,
      hasPrevPage: Number(page) > 1
    }
  }
})
