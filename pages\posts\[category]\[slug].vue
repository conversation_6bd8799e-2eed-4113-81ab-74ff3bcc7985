<template>
  <div>
    <div v-if="pending" class="space-y-4">
      <div class="skeleton h-8 w-3/4"></div>
      <div class="skeleton h-4 w-1/2"></div>
      <div class="skeleton h-64 w-full"></div>
    </div>
    
    <div v-else-if="error" class="alert alert-error">
      <span>文章加载失败</span>
    </div>
    
    <article v-else class="prose prose-lg max-w-none">
      <header class="mb-8">
        <h1 class="text-4xl font-bold mb-4">{{ data?.title }}</h1>
        <div class="flex items-center gap-4 text-base-content/60">
          <div class="badge badge-primary">{{ data?.category.name }}</div>
          <time>{{ formatDate(data?.created) }}</time>
          <span v-if="data?.updated !== data?.created">
            更新于 {{ formatDate(data?.updated) }}
          </span>
        </div>
      </header>
      
      <div class="divider"></div>
      
      <div class="prose-content" v-html="renderedContent"></div>
    </article>
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const { category, slug } = route.params

// Page meta
definePageMeta({
  validate: async (route) => {
    return typeof route.params.category === 'string' && 
           typeof route.params.slug === 'string'
  }
})

// Data fetching
const { data, pending, error } = await useFetch(`/api/posts/${category}/${slug}`)

// SEO
useHead({
  title: () => data.value?.title || '文章详情',
  meta: [
    { name: 'description', content: () => data.value?.summary || '' },
    { property: 'og:title', content: () => data.value?.title || '' },
    { property: 'og:description', content: () => data.value?.summary || '' },
    { property: 'og:type', content: 'article' }
  ]
})

// Render markdown content (simplified - you might want to use a proper markdown renderer)
const renderedContent = computed(() => {
  if (!data.value?.content) return ''
  
  // Simple markdown to HTML conversion (replace with proper markdown parser)
  return data.value.content
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/\n/gim, '<br>')
})

// Utility function
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>

<style scoped>
.prose-content {
  @apply text-base-content leading-relaxed;
}

.prose-content h1,
.prose-content h2,
.prose-content h3 {
  @apply font-bold text-base-content mt-8 mb-4;
}

.prose-content h1 {
  @apply text-2xl;
}

.prose-content h2 {
  @apply text-xl;
}

.prose-content h3 {
  @apply text-lg;
}

.prose-content p {
  @apply mb-4;
}

.prose-content strong {
  @apply font-semibold;
}

.prose-content em {
  @apply italic;
}
</style>
