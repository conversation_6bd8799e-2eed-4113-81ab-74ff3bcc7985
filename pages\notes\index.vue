<template>
  <div>
    <h1 class="text-3xl font-bold mb-8">笔记列表</h1>
    
    <div v-if="pending" class="space-y-4">
      <div v-for="i in 5" :key="i" class="skeleton h-32 w-full"></div>
    </div>
    
    <div v-else-if="error" class="alert alert-error">
      <span>加载笔记失败</span>
    </div>
    
    <div v-else class="space-y-6">
      <article 
        v-for="note in data?.data" 
        :key="note.id"
        class="card bg-base-100 shadow-xl"
      >
        <div class="card-body">
          <h2 class="card-title">
            <NuxtLink 
              :to="`/notes/${note.nid}`"
              class="hover:text-primary transition-colors"
            >
              {{ note.title }}
            </NuxtLink>
          </h2>
          <p class="text-base-content/70">{{ note.summary }}</p>
          <div class="card-actions justify-end">
            <div class="text-sm text-base-content/50">
              {{ formatDate(note.created) }}
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</template>

<script setup lang="ts">
// Page meta
definePageMeta({
  title: '笔记'
})

// SEO
useHead({
  title: '笔记列表',
  meta: [
    { name: 'description', content: '查看所有笔记' }
  ]
})

// Data fetching
const { data, pending, error } = await useFetch('/api/notes')

// Utility function
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>
