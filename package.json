{"name": "<PERSON><PERSON>", "version": "1.2.5", "private": false, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912", "license": "AGPL-3.0", "engines": {"node": ">=20"}, "scripts": {"analyze": "cross-env NODE_ENV=production ANALYZE=true BUNDLE_ANALYZE=browser next build", "prebuild": "rimraf .next || exit 0", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max_old_space_size=4096 next build", "build:ci": "cross-env NODE_ENV=production NODE_OPTIONS=--max_old_space_size=4096 NEXT_TELEMETRY_DISABLED=1 CI=true next build", "dev": "cross-env NODE_ENV=development next dev -p 2323", "dev:turbo": "cross-env NODE_ENV=development next dev -p 2323 --turbo", "lint": "eslint --fix", "prepare": "pnpm exec simple-git-hooks && test -f .env || cp .env.template .env", "prod:pm2": "cross-env NODE_ENV=production pm2 restart ecosystem.config.cjs", "prod:reload": "pm2 reload ecosystem.config.cjs", "prod:stop": "pm2 stop ecosystem.config.cjs", "start": "npm run dev"}, "dependencies": {"@aws-sdk/client-s3": "3.782.0", "@excalidraw/excalidraw": "0.18.0", "@floating-ui/react-dom": "2.1.3", "@mx-space/api-client": "1.17.0", "@openpanel/nextjs": "1.0.8", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@shikijs/transformers": "3.4.2", "@shiro/fetch": "link:./packages/fetch", "@simplewebauthn/browser": "13.1.0", "@simplewebauthn/types": "12.0.0", "@tanstack/query-async-storage-persister": "5.76.2", "@tanstack/react-query": "5.80.7", "@tanstack/react-query-devtools": "5.80.7", "@tanstack/react-query-persist-client": "5.80.7", "@upstash/redis": "1.34.9", "@vercel/analytics": "1.5.0", "@vercel/postgres": "0.10.0", "better-auth": "1.2.8", "blurhash": "2.0.5", "bypass-vue-devtools": "0.0.6", "chroma-js": "3.1.2", "clsx": "2.1.1", "colorjs.io": "^0.5.2", "crossbell": "1.12.1", "daisyui": "4.12.24", "dayjs": "1.11.13", "emoji-mart": "5.6.0", "emoji-picker-react": "4.12.2", "es-toolkit": "1.39.3", "exif-js": "2.3.0", "foxact": "0.2.49", "fuse.js": "7.1.0", "idb-keyval": "6.2.2", "immer": "^10.1.1", "jojoo": "0.1.4", "jotai": "2.12.5", "js-cookie": "3.0.5", "js-yaml": "4.1.0", "jsondiffpatch": "^0.7.3", "katex": "^0.16.22", "kbar": "0.1.0-beta.45", "markdown-escape": "2.0.0", "markdown-to-jsx": "npm:@innei/markdown-to-jsx@7.4.5-fork.4", "marked": "15.0.12", "medium-zoom": "1.1.0", "mermaid": "11.6.0", "motion": "12.18.1", "nanoid": "^5.1.5", "next": "14.2.8", "next-runtime-env": "3.3.0", "next-themes": "0.4.6", "ofetch": "1.4.1", "openai": "5.3.0", "pngjs": "7.0.0", "prop-types": "15.8.1", "qrcode.react": "4.2.0", "react": "18.3.1", "react-blurhash": "0.3.0", "react-dom": "18.3.1", "react-error-boundary": "6.0.0", "react-intersection-observer": "9.16.0", "react-photo-view": "1.2.7", "react-responsive-masonry": "2.7.1", "react-router-dom": "7.6.2", "react-shadow": "20.6.0", "react-tweet": "3.2.2", "remark-directive": "3.0.1", "remove-markdown": "0.6.2", "rss": "1.2.2", "server-only": "^0.0.1", "socket.io-client": "4.8.1", "sonner": "2.0.5", "tailwind-merge": "3.3.1", "unidata.js": "0.8.0", "unified": "^11.0.5", "uniqolor": "1.1.1", "unist-util-visit": "5.0.0", "use-context-selector": "2.0.0", "vaul": "1.1.2", "xss": "1.0.15"}, "devDependencies": {"@egoist/tailwindcss-icons": "1.9.0", "@iconify-json/material-symbols": "1.2.26", "@iconify-json/mingcute": "1.2.3", "@innei/prettier": "0.15.0", "@mx-space/webhook": "0.5.0", "@next/bundle-analyzer": "15.3.3", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/typography": "0.5.16", "@types/chroma-js": "3.1.1", "@types/js-cookie": "3.0.6", "@types/js-yaml": "4.0.9", "@types/katex": "^0.16.7", "@types/markdown-escape": "1.1.3", "@types/node": "24.0.1", "@types/pngjs": "6.0.5", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "@types/react-responsive-masonry": "2.6.0", "@types/remove-markdown": "0.3.4", "@types/rss": "0.0.32", "autoprefixer": "10.4.21", "client-only": "0.0.1", "code-inspector-plugin": "0.20.12", "cross-env": "7.0.3", "dotenv": "16.5.0", "eslint": "9.29.0", "eslint-config-hyoban": "4.0.8", "lint-staged": "16.1.2", "postcss": "8.5.5", "postcss-import": "16.1.0", "postcss-js": "4.0.1", "postcss-nested": "7.0.2", "postcss-preset-env": "10.2.3", "postcss-prune-var": "1.1.2", "prettier": "3.5.3", "rimraf": "6.0.1", "shiki": "3.6.0", "simple-git-hooks": "2.13.0", "tailwind-scrollbar": "4.0.2", "tailwind-variants": "0.3.1", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7", "tailwindcss-animated": "2.0.0", "tailwindcss-motion": "1.1.1", "typescript": "5.8.3", "zx": "8.5.5"}, "pnpm": {"overrides": {"array-includes": "npm:@nolyfill/array-includes@latest", "array.prototype.findlastindex": "npm:@nolyfill/array.prototype.findlastindex@latest", "array.prototype.flat": "npm:@nolyfill/array.prototype.flat@latest", "array.prototype.flatmap": "npm:@nolyfill/array.prototype.flatmap@latest", "array.prototype.tosorted": "npm:@nolyfill/array.prototype.tosorted@latest", "has": "npm:@nolyfill/has@latest", "object.assign": "npm:@nolyfill/object.assign@latest", "object.entries": "npm:@nolyfill/object.entries@latest", "object.fromentries": "npm:@nolyfill/object.fromentries@latest", "object.groupby": "npm:@nolyfill/object.groupby@latest", "object.hasown": "npm:@nolyfill/object.hasown@latest", "object.values": "npm:@nolyfill/object.values@latest", "string.prototype.matchall": "npm:@nolyfill/string.prototype.matchall@latest"}}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --ignore-path ./.gitignore --write "], "*.{js,ts,cjs,mjs,jsx,tsx,json}": ["eslint --fix"]}, "bump": {"before": ["git pull --rebase", "ni"], "changelog": true}, "nextBundleAnalysis": {"budget": 358400, "budgetPercentIncreaseRed": 20, "showDetails": true}, "browserslist": ["defaults and fully supports es6-module"]}