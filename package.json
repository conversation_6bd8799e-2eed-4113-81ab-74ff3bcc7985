{"name": "<PERSON><PERSON>", "version": "1.2.5", "private": false, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912", "license": "AGPL-3.0", "engines": {"node": ">=20"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev --port 2323", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint --fix", "prepare": "pnpm exec simple-git-hooks && test -f .env || cp .env.template .env", "start": "npm run dev"}, "dependencies": {"@aws-sdk/client-s3": "3.782.0", "@excalidraw/excalidraw": "0.18.0", "@floating-ui/vue": "1.1.5", "@headlessui/vue": "1.7.23", "@mx-space/api-client": "1.17.0", "@nuxt/image": "1.8.1", "@pinia/nuxt": "0.5.5", "@shikijs/transformers": "3.4.2", "@shiro/fetch": "link:./packages/fetch", "@simplewebauthn/browser": "13.1.0", "@simplewebauthn/types": "12.0.0", "@upstash/redis": "1.34.9", "@vercel/analytics": "1.5.0", "@vercel/postgres": "0.10.0", "@vueuse/core": "11.3.0", "@vueuse/nuxt": "11.3.0", "better-auth": "1.2.8", "blurhash": "2.0.5", "chroma-js": "3.1.2", "clsx": "2.1.1", "colorjs.io": "^0.5.2", "crossbell": "1.12.1", "daisyui": "4.12.24", "dayjs": "1.11.13", "es-toolkit": "1.39.3", "exif-js": "2.3.0", "fuse.js": "7.1.0", "immer": "^10.1.1", "js-cookie": "3.0.5", "js-yaml": "4.1.0", "jsondiffpatch": "^0.7.3", "katex": "^0.16.22", "markdown-escape": "2.0.0", "markdown-to-jsx": "npm:@innei/markdown-to-jsx@7.4.5-fork.4", "marked": "15.0.12", "medium-zoom": "1.1.0", "mermaid": "11.6.0", "nanoid": "^5.1.5", "nuxt": "3.14.159", "ofetch": "1.4.1", "openai": "5.3.0", "pinia": "2.2.8", "pngjs": "7.0.0", "qrcode": "1.5.4", "remark-directive": "3.0.1", "remove-markdown": "0.6.2", "rss": "1.2.2", "tailwind-merge": "3.3.1", "unidata.js": "0.8.0", "unified": "^11.0.5", "uniqolor": "1.1.1", "unist-util-visit": "5.0.0", "vue": "3.5.13", "vue-router": "4.5.0", "xss": "1.0.15"}, "devDependencies": {"@egoist/tailwindcss-icons": "1.9.0", "@iconify-json/material-symbols": "1.2.26", "@iconify-json/mingcute": "1.2.3", "@innei/prettier": "0.15.0", "@mx-space/webhook": "0.5.0", "@nuxt/devtools": "1.6.0", "@nuxtjs/color-mode": "3.5.1", "@nuxtjs/tailwindcss": "6.12.2", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/typography": "0.5.16", "@types/chroma-js": "3.1.1", "@types/js-cookie": "3.0.6", "@types/js-yaml": "4.0.9", "@types/katex": "^0.16.7", "@types/markdown-escape": "1.1.3", "@types/node": "24.0.1", "@types/pngjs": "6.0.5", "@types/remove-markdown": "0.3.4", "@types/rss": "0.0.32", "autoprefixer": "10.4.21", "cross-env": "7.0.3", "dotenv": "16.5.0", "eslint": "9.29.0", "eslint-config-hyoban": "4.0.8", "lint-staged": "16.1.2", "postcss": "8.5.5", "postcss-import": "16.1.0", "postcss-js": "4.0.1", "postcss-nested": "7.0.2", "postcss-preset-env": "10.2.3", "postcss-prune-var": "1.1.2", "prettier": "3.5.3", "rimraf": "6.0.1", "shiki": "3.6.0", "simple-git-hooks": "2.13.0", "tailwind-scrollbar": "4.0.2", "tailwind-variants": "0.3.1", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7", "tailwindcss-animated": "2.0.0", "tailwindcss-motion": "1.1.1", "typescript": "5.8.3", "vue-tsc": "2.1.10", "zx": "8.5.5"}, "pnpm": {"overrides": {"array-includes": "npm:@nolyfill/array-includes@latest", "array.prototype.findlastindex": "npm:@nolyfill/array.prototype.findlastindex@latest", "array.prototype.flat": "npm:@nolyfill/array.prototype.flat@latest", "array.prototype.flatmap": "npm:@nolyfill/array.prototype.flatmap@latest", "array.prototype.tosorted": "npm:@nolyfill/array.prototype.tosorted@latest", "has": "npm:@nolyfill/has@latest", "object.assign": "npm:@nolyfill/object.assign@latest", "object.entries": "npm:@nolyfill/object.entries@latest", "object.fromentries": "npm:@nolyfill/object.fromentries@latest", "object.groupby": "npm:@nolyfill/object.groupby@latest", "object.hasown": "npm:@nolyfill/object.hasown@latest", "object.values": "npm:@nolyfill/object.values@latest", "string.prototype.matchall": "npm:@nolyfill/string.prototype.matchall@latest"}}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --ignore-path ./.gitignore --write "], "*.{js,ts,cjs,mjs,jsx,tsx,json}": ["eslint --fix"]}, "bump": {"before": ["git pull --rebase", "ni"], "changelog": true}, "nextBundleAnalysis": {"budget": 358400, "budgetPercentIncreaseRed": 20, "showDetails": true}, "browserslist": ["defaults and fully supports es6-module"]}