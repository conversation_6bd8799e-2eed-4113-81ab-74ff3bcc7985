export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { size = 10, page = 1 } = query

  // Mock data - replace with real API calls
  const mockNotes = [
    {
      id: '1',
      nid: 1,
      title: '今天学习了 Vue 3 Composition API',
      summary: '记录一下 Vue 3 Composition API 的学习心得',
      content: '# Vue 3 Composition API 学习笔记\n\n今天深入学习了...',
      created: '2024-01-01T00:00:00Z',
      updated: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      nid: 2,
      title: '关于代码重构的思考',
      summary: '在重构项目代码时的一些思考和总结',
      content: '# 代码重构思考\n\n最近在重构一个老项目...',
      created: '2024-01-02T00:00:00Z',
      updated: '2024-01-02T00:00:00Z'
    },
    {
      id: '3',
      nid: 3,
      title: '读书笔记：《代码整洁之道》',
      summary: '《代码整洁之道》这本书的读书笔记',
      content: '# 《代码整洁之道》读书笔记\n\n这本书强调了...',
      created: '2024-01-03T00:00:00Z',
      updated: '2024-01-03T00:00:00Z'
    }
  ]

  // Simulate pagination
  const startIndex = (Number(page) - 1) * Number(size)
  const endIndex = startIndex + Number(size)
  const paginatedNotes = mockNotes.slice(startIndex, endIndex)

  return {
    data: paginatedNotes,
    pagination: {
      currentPage: Number(page),
      totalPages: Math.ceil(mockNotes.length / Number(size)),
      totalItems: mockNotes.length,
      hasNextPage: endIndex < mockNotes.length,
      hasPrevPage: Number(page) > 1
    }
  }
})
