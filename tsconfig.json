{"extends": "./.nuxt/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "incremental": true, "paths": {"~/*": ["./*"], "@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.vue", "packages/fetch/index.js"], "exclude": ["node_modules", "packages/markdown", "./storybook", "dist"]}