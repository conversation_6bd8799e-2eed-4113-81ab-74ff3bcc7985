/**
 * API client composable for making HTTP requests
 */
export const useApi = () => {
  const config = useRuntimeConfig()
  
  const apiBase = config.public.apiBase
  
  // Create a custom $fetch instance with base configuration
  const api = $fetch.create({
    baseURL: apiBase,
    headers: {
      'Content-Type': 'application/json'
    },
    onRequest({ request, options }) {
      // Add authentication headers if needed
      const token = useCookie('auth-token')
      if (token.value) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token.value}`
        }
      }
    },
    onResponseError({ response }) {
      // Handle global error responses
      if (response.status === 401) {
        // Redirect to login or clear auth
        navigateTo('/login')
      }
    }
  })
  
  return {
    // Posts API
    posts: {
      list: (params?: { page?: number; size?: number; category?: string }) =>
        api('/posts', { query: params }),
      
      get: (categorySlug: string, slug: string) =>
        api(`/posts/${categorySlug}/${slug}`),
      
      categories: () =>
        api('/categories')
    },
    
    // Notes API
    notes: {
      list: (params?: { page?: number; size?: number }) =>
        api('/notes', { query: params }),
      
      get: (nid: number) =>
        api(`/notes/${nid}`)
    },
    
    // Says API
    says: {
      list: (params?: { page?: number; size?: number }) =>
        api('/says', { query: params })
    },
    
    // Timeline API
    timeline: {
      list: (params?: { page?: number; size?: number; year?: number }) =>
        api('/timeline', { query: params })
    },
    
    // Stats API
    stats: {
      get: () =>
        api('/stats')
    }
  }
}

/**
 * Composable for handling paginated data
 */
export const usePagination = <T>(
  fetcher: (page: number) => Promise<{ data: T[]; pagination: any }>,
  initialPage = 1
) => {
  const currentPage = ref(initialPage)
  const data = ref<T[]>([])
  const pagination = ref<any>({})
  const loading = ref(false)
  const error = ref<Error | null>(null)
  
  const loadPage = async (page: number) => {
    loading.value = true
    error.value = null
    
    try {
      const result = await fetcher(page)
      data.value = result.data
      pagination.value = result.pagination
      currentPage.value = page
    } catch (err) {
      error.value = err as Error
    } finally {
      loading.value = false
    }
  }
  
  const nextPage = () => {
    if (pagination.value.hasNextPage) {
      loadPage(currentPage.value + 1)
    }
  }
  
  const prevPage = () => {
    if (pagination.value.hasPrevPage) {
      loadPage(currentPage.value - 1)
    }
  }
  
  // Load initial page
  loadPage(initialPage)
  
  return {
    data: readonly(data),
    pagination: readonly(pagination),
    loading: readonly(loading),
    error: readonly(error),
    currentPage: readonly(currentPage),
    loadPage,
    nextPage,
    prevPage
  }
}
