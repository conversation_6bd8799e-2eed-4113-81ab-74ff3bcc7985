<template>
  <div class="min-h-screen bg-base-100">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-base-100/80 backdrop-blur-md border-b border-base-300">
      <div class="container mx-auto px-4 py-4">
        <nav class="flex items-center justify-between">
          <NuxtLink to="/" class="text-xl font-bold text-primary">
            Shiro
          </NuxtLink>
          
          <div class="hidden md:flex items-center space-x-6">
            <NuxtLink to="/posts" class="hover:text-primary transition-colors">
              文章
            </NuxtLink>
            <NuxtLink to="/notes" class="hover:text-primary transition-colors">
              笔记
            </NuxtLink>
            <NuxtLink to="/says" class="hover:text-primary transition-colors">
              说说
            </NuxtLink>
            <NuxtLink to="/timeline" class="hover:text-primary transition-colors">
              时间线
            </NuxtLink>
            <NuxtLink to="/friends" class="hover:text-primary transition-colors">
              朋友
            </NuxtLink>
          </div>

          <!-- Theme toggle -->
          <button 
            @click="toggleColorMode"
            class="btn btn-ghost btn-circle"
            aria-label="切换主题"
          >
            <Icon 
              :name="$colorMode.value === 'dark' ? 'mingcute:sun-line' : 'mingcute:moon-line'" 
              class="w-5 h-5" 
            />
          </button>
        </nav>
      </div>
    </header>

    <!-- Main content -->
    <main class="container mx-auto px-4 py-8">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-base-200 mt-16">
      <div class="container mx-auto px-4 py-8">
        <div class="text-center text-base-content/60">
          <p>&copy; {{ new Date().getFullYear() }} Shiro. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
const { $colorMode } = useNuxtApp()

const toggleColorMode = () => {
  $colorMode.preference = $colorMode.value === 'dark' ? 'light' : 'dark'
}
</script>

<style scoped>
/* Layout specific styles */
</style>
