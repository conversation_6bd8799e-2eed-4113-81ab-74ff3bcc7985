import type { SVGProps } from 'react'
import * as React from 'react'

export function SiGlyphGlobal(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 16 16" {...props}>
      <g fill="currentColor" fillRule="evenodd">
        <path d="M8.048 0a8 8 0 1 0 .001 16.001A8 8 0 0 0 8.048 0zM8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14z" />
        <path d="M2.959 2.684c-.27 1.344.735 3.399 2.872 3.899c2.136.5 1.218.084.886-.583c-.334-.667-.035-1.167.482-1.333c.518-.167.387.293 1.155-.667c.194-2-1.472-.027-1.638-.86C8.366 1.223 5.95.5 5.249.75c-.701.25-2.02.589-2.29 1.934zm3.914 5.01c-.124.055-.702.416-.901.666c-.199.25-.286.778 0 1c.286.223-.016 1.279.755 1.473c.771.194 1.543.307 1.572.917c.027.61-.072 2.027-.443 2.167c.5.277 1.717-1.195 2.145-1.973c.429-.777.572-1.889.543-2.167c-.029-.277.171-.86.257-1.61c.085-.75-.57-.8-.655-1.133c-.087-.334-.203-.701-3.273.66zM13.125 4s-2.012.861-.644 2.14c1.366 1.277 2.062 2.49 1.995 2.768c-.062.276-1.38 1.706-.623 1.594c.761-.111 1.322-1.611 1.443-2.501c.119-.89-.924-2.639-.967-2.777c-.036-.141-.722-1.209-1.204-1.224z" />
      </g>
    </svg>
  )
}

export function PhUser(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 256 256" {...props}>
      <path
        fill="currentColor"
        d="M231.9 212a120.7 120.7 0 0 0-67.1-54.2a72 72 0 1 0-73.6 0A120.7 120.7 0 0 0 24.1 212a8 8 0 1 0 13.8 8a104.1 104.1 0 0 1 180.2 0a8 8 0 1 0 13.8-8ZM72 96a56 56 0 1 1 56 56a56 56 0 0 1-56-56Z"
      />
    </svg>
  )
}

export function MdiEmailFastOutline(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 24" {...props}>
      <path
        fill="currentColor"
        d="M22 5.5H9c-1.1 0-2 .9-2 2v9a2 2 0 0 0 2 2h13c1.11 0 2-.89 2-2v-9a2 2 0 0 0-2-2m0 11H9V9.17l6.5 3.33L22 9.17v7.33m-6.5-5.69L9 7.5h13l-6.5 3.31M5 16.5c0 .17.03.33.05.5H1c-.552 0-1-.45-1-1s.448-1 1-1h4v1.5M3 7h2.05c-.02.17-.05.33-.05.5V9H3c-.55 0-1-.45-1-1s.45-1 1-1m-2 5c0-.55.45-1 1-1h3v2H2c-.55 0-1-.45-1-1Z"
      />
    </svg>
  )
}

export function GridiconsNoticeOutline(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8s-8-3.589-8-8s3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10s10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"
      />
    </svg>
  )
}

export function LaUserSecret(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <path
        fill="currentColor"
        d="M13.063 4c-.876 0-1.645.45-2.188 1.031c-.543.582-.934 1.309-1.281 2.094c-.531 1.21-.91 2.555-1.25 3.813c-1.086.316-2.008.71-2.75 1.187C4.727 12.684 4 13.457 4 14.5c0 .906.555 1.633 1.25 2.156c.594.446 1.324.817 2.188 1.125c.05.23.125.465.218.688c-.843.476-2.18 1.398-3.468 3.156l-.594.844l.844.593l3.28 2.25L6.376 28h19.25l-1.344-2.688l3.282-2.25l.843-.593l-.593-.844c-1.29-1.758-2.625-2.68-3.47-3.156a3.93 3.93 0 0 0 .22-.688c.863-.308 1.593-.68 2.187-1.125c.695-.523 1.25-1.25 1.25-2.156c0-1.043-.727-1.816-1.594-2.375c-.742-.477-1.664-.871-2.75-1.188c-.375-1.304-.789-2.671-1.312-3.874c-.34-.778-.715-1.493-1.25-2.063c-.535-.57-1.297-1-2.157-1c-.582 0-1.023.16-1.5.281c-.476.121-.957.219-1.437.219c-.96 0-1.766-.5-2.938-.5zm0 2c.207 0 1.437.5 2.937.5c.75 0 1.418-.152 1.938-.281c.519-.13.914-.219 1-.219c.23 0 .402.074.687.375c.285.3.621.844.906 1.5c.543 1.242.957 2.938 1.407 4.5c0-.004.054-.047-.094.031c-.25.137-.774.313-1.407.406c-1.269.192-3 .188-4.437.188c-1.43 0-3.164-.02-4.438-.219c-.636-.097-1.152-.27-1.406-.406c-.078-.043-.105-.027-.125-.031v-.031c.004-.008-.004-.024 0-.032l.031-.031a1.01 1.01 0 0 0 .126-.438v-.03c.359-1.329.761-2.735 1.25-3.845c.292-.667.609-1.21.906-1.53c.297-.321.5-.407.719-.407zm-4.876 7.094c.227.469.626.844 1.032 1.062c.61.324 1.308.477 2.062.594c1.508.234 3.274.25 4.719.25c1.438 0 3.207.008 4.719-.219c.758-.113 1.449-.261 2.062-.594c.41-.222.809-.617 1.032-1.093c.617.219 1.136.453 1.5.687c.582.375.687.653.687.719c0 .059-.05.25-.469.563c-.418.312-1.136.675-2.062.968c-1.852.59-4.516.969-7.469.969c-2.953 0-5.617-.379-7.469-.969c-.926-.293-1.644-.656-2.062-.968C6.05 14.75 6 14.559 6 14.5c0-.066.078-.316.656-.688c.364-.234.899-.488 1.532-.718zm2.594 5.469c.328.054.653.144 1 .187c.13.879.813 1.652 1.906 1.719c.844.05 1.793-.348 1.876-1.469h.875c.082 1.121 1.03 1.52 1.875 1.469c1.093-.067 1.777-.84 1.906-1.719c.347-.043.672-.133 1-.188l-.094.625c-.309 1.645-1.043 3.168-1.969 4.22C18.23 24.456 17.145 25.015 16 25c-1.176-.016-2.238-.582-3.156-1.625c-.918-1.043-1.64-2.535-1.969-4.188zM23 20c.371.219 1.348.86 2.469 2.094l-3.032 2.093l-.718.47l.375.78l.281.563h-3.156a7.547 7.547 0 0 0 1.437-1.281c1.102-1.25 1.84-2.887 2.25-4.657c.035-.019.063-.042.094-.062zm-14.031.031c.039.024.086.04.125.063c.43 1.746 1.164 3.363 2.25 4.593c.449.512.972.95 1.531 1.313h-3.25l.281-.563l.375-.78l-.719-.47l-3.03-2.093c1.058-1.168 2.023-1.813 2.437-2.063z"
      />
    </svg>
  )
}

export function RadixIconsAvatar(props: SVGProps<SVGSVGElement>) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 15 15" {...props}>
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M.877 7.5a6.623 6.623 0 1 1 13.246 0a6.623 6.623 0 0 1-13.246 0ZM7.5 1.827a5.673 5.673 0 0 0-4.193 9.494A4.971 4.971 0 0 1 7.5 9.025a4.97 4.97 0 0 1 4.193 2.296A5.673 5.673 0 0 0 7.5 1.827Zm3.482 10.152A4.023 4.023 0 0 0 7.5 9.975a4.023 4.023 0 0 0-3.482 2.004A5.648 5.648 0 0 0 7.5 13.173c1.312 0 2.52-.446 3.482-1.194ZM5.15 6.505a2.35 2.35 0 1 1 4.7 0a2.35 2.35 0 0 1-4.7 0Zm2.35-1.4a1.4 1.4 0 1 0 0 2.8a1.4 1.4 0 0 0 0-2.8Z"
        clipRule="evenodd"
      />
    </svg>
  )
}
