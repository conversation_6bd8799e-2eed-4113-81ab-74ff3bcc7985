.comment__message {
  * {
    @apply break-words leading-6;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-lg font-bold;
  }

  h2 {
    font-size: 1.065rem;
    line-height: 1.75rem;
    @apply font-bold;
  }

  hr {
    @apply my-1.5 border-zinc-400 opacity-20;
  }

  ul {
    @apply list-disc pl-4;
  }

  ol {
    @apply list-decimal pl-4;
  }

  blockquote {
    @apply pl-3;
    @apply relative mb-2 border-0 text-base-content/60;

    &::before {
      content: '';
      display: block;
      width: 3px;
      bottom: 5px;
      position: absolute;
      left: 0;
      top: 5px;
      border-radius: 24px;
      background-color: theme(colors.accent);
    }
  }

  img,
  video {
    @apply my-2 rounded-md;

    max-height: 350px;
  }

  :global(.shiki-block) {
    @apply my-1;
  }
}

[data-theme='dark'] .comment__message {
  hr {
    @apply border-zinc-100 opacity-20;
  }

  blockquote {
    @apply border-zinc-50/50;
  }
}
