{"scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@radix-ui/react-scroll-area": "1.2.9", "marked": "^15.0.12", "postcss-import": "^16.1.0", "react-error-boundary": "5.0.0", "react-router-dom": "7.5.3", "vite": "6.2.7", "vite-plugin-node-polyfills": "0.23.0", "vite-plugin-restart": "0.4.2"}, "devDependencies": {"@mdx-js/react": "3.1.0", "@mdx-js/rollup": "3.1.0", "@types/lodash-es": "4.17.12", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.4", "buffer": "6.0.3", "concurrently": "^9.1.2", "unplugin-macros": "0.16.3", "vite-tsconfig-paths": "^5.1.4"}}