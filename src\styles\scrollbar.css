body::-webkit-scrollbar {
  height: 0;
}

html {
  /*  for firefox */
  scrollbar-color: theme(colors.gray.300);
  scrollbar-width: thin;
}

html.dark {
  scrollbar-color: theme(colors.muted.500);
}

body {
  overflow: overlay;
  /* scrollbar-gutter: stable; */
}

[data-theme='dark'] {
  *::-webkit-scrollbar-thumb {
    border: 3px solid theme(colors.muted.500);
  }

  *::-webkit-scrollbar-thumb:hover {
    background: theme(colors.muted.500/0.5);
  }
}

*::-webkit-scrollbar-thumb {
  background-color: transparent;
  border: 3px solid theme(colors.gray.300);
  @apply rounded-xl;
}

*::-webkit-scrollbar-thumb:hover {
  border-color: theme(colors.muted.500/0.8);
}

*::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: theme(colors.muted.500);
}

*::-webkit-scrollbar-thumb:hover {
  background: theme(colors.muted.500/0.8);
}

*::-webkit-scrollbar-corner {
  background: theme(colors.zinc.100);
}
