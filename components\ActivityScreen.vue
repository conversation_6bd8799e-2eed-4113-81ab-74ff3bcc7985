<template>
  <section class="py-16">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">最新动态</h2>
      
      <div class="grid md:grid-cols-2 gap-8">
        <!-- Recent Posts -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h3 class="card-title">
              <Icon name="mingcute:document-line" class="w-5 h-5" />
              最新文章
            </h3>
            <div v-if="postsLoading" class="space-y-2">
              <div v-for="i in 3" :key="i" class="skeleton h-4 w-full"></div>
            </div>
            <div v-else class="space-y-3">
              <div 
                v-for="post in recentPosts" 
                :key="post.id"
                class="border-l-2 border-primary pl-3"
              >
                <NuxtLink 
                  :to="`/posts/${post.category.slug}/${post.slug}`"
                  class="font-medium hover:text-primary transition-colors"
                >
                  {{ post.title }}
                </NuxtLink>
                <p class="text-sm text-base-content/60">
                  {{ formatDate(post.created) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Notes -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h3 class="card-title">
              <Icon name="mingcute:edit-line" class="w-5 h-5" />
              最新笔记
            </h3>
            <div v-if="notesLoading" class="space-y-2">
              <div v-for="i in 3" :key="i" class="skeleton h-4 w-full"></div>
            </div>
            <div v-else class="space-y-3">
              <div 
                v-for="note in recentNotes" 
                :key="note.id"
                class="border-l-2 border-secondary pl-3"
              >
                <NuxtLink 
                  :to="`/notes/${note.nid}`"
                  class="font-medium hover:text-secondary transition-colors"
                >
                  {{ note.title }}
                </NuxtLink>
                <p class="text-sm text-base-content/60">
                  {{ formatDate(note.created) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Fetch recent posts
const { data: postsData, pending: postsLoading } = await useFetch('/api/posts', {
  query: { size: 3 }
})

// Fetch recent notes
const { data: notesData, pending: notesLoading } = await useFetch('/api/notes', {
  query: { size: 3 }
})

const recentPosts = computed(() => postsData.value?.data || [])
const recentNotes = computed(() => notesData.value?.data || [])

// Utility function
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>
