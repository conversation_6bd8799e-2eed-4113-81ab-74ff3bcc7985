name: 'Next.js Bundle Analysis'

on:
  pull_request:
  push:
    branches:
      - main # change this if your default branch is named differently
      - master
      - dev
  workflow_dispatch:

defaults:
  run:
    # change this if your nextjs app does not live at the root of the repo
    working-directory: ./

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # If pnpm is used, you need to switch the previous step with the following one. pnpm does not create a package-lock.json
      # so the step above will fail to pull dependencies
      - uses: pnpm/action-setup@v4
        name: Install pnpm
        id: pnpm-install
        with:
          run_install: true

      - name: Restore next build
        uses: actions/cache@v4
        id: restore-build-cache
        env:
          cache-name: cache-next-build
        with:
          # if you use a custom build directory, replace all instances of `.next` in this file with your build directory
          # ex: if your app builds to `dist`, replace `.next` with `dist`
          path: .next/cache
          # change this if you prefer a more strict cache
          key: ${{ runner.os }}-build-${{ env.cache-name }}

      - name: Build next.js app
        # change this if your site requires a custom build command
        run: ./node_modules/.bin/next build

      # Here's the first place where next-bundle-analysis' own script is used
      # This step pulls the raw bundle stats for the current bundle
      - name: Analyze bundle
        run: npx -p nextjs-bundle-analysis report

      - name: Upload bundle
        uses: actions/upload-artifact@v4
        with:
          name: bundle
          path: .next/analyze/__bundle_analysis.json

      - name: Download base branch bundle stats
        uses: dawidd6/action-download-artifact@v10
        if: success() && github.event.number
        with:
          workflow: nextjs_bundle_analysis.yml
          branch: ${{ github.event.pull_request.base.ref }}
          path: .next/analyze/base

      - name: Compare with base branch bundle
        if: success() && github.event.number
        run: ls -laR .next/analyze/base && npx -p nextjs-bundle-analysis compare

      - name: Upload analysis
        if: success() && github.event.number
        uses: actions/upload-artifact@v4
        with:
          name: analysis_comment
          path: .next/analyze/__bundle_analysis_comment.txt

      - name: Save PR number
        if: ${{ always() }}
        run: echo ${{ github.event.number }} > ./pr-id.txt

      - name: Upload PR number
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: pr
          path: ./pr-id.txt
