export default defineEventHandler(async (event) => {
  const { category, slug } = getRouterParams(event)

  // Mock data - replace with real API calls
  const mockPost = {
    id: '1',
    title: '欢迎使用 Nuxt.js',
    slug: 'welcome-to-nuxtjs',
    summary: '这是一篇关于 Nuxt.js 的介绍文章',
    content: `# 欢迎使用 Nuxt.js

Nuxt.js 是一个基于 Vue.js 的框架，它提供了许多开箱即用的功能，让你能够快速构建现代化的 Web 应用程序。

## 主要特性

- **服务端渲染 (SSR)**：提供更好的 SEO 和首屏加载性能
- **静态站点生成 (SSG)**：可以生成静态网站
- **自动路由**：基于文件系统的路由
- **TypeScript 支持**：内置 TypeScript 支持
- **模块化**：丰富的模块生态系统

## 开始使用

要开始使用 Nuxt.js，你可以运行以下命令：

\`\`\`bash
npx nuxi@latest init my-app
cd my-app
npm install
npm run dev
\`\`\`

这将创建一个新的 Nuxt.js 项目并启动开发服务器。

## 总结

Nuxt.js 是一个强大的框架，它简化了 Vue.js 应用程序的开发过程。无论你是构建单页应用程序、多页应用程序还是静态网站，Nuxt.js 都能满足你的需求。`,
    created: '2024-01-01T00:00:00Z',
    updated: '2024-01-01T00:00:00Z',
    category: {
      id: '1',
      name: '技术',
      slug: 'tech'
    }
  }

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))

  // Check if the requested post exists
  if (category === 'tech' && slug === 'welcome-to-nuxtjs') {
    return mockPost
  }

  // Return 404 if post not found
  throw createError({
    statusCode: 404,
    statusMessage: 'Post not found'
  })
})
