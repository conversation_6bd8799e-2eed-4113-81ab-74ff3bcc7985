@import 'tailwind.css';

/* CSS Variables */
html {
  --font-sans: theme('fontFamily.sans');

  /* Accent color for form controls */
  accent-color: theme(colors.accent);

  /* make app like native app in mobile */
  -webkit-tap-highlight-color: transparent;

  font-family: var(--font-sans), system-ui, -apple-system, PingFang SC, "Microsoft YaHei", Segoe UI, Roboto, Helvetica, noto sans sc, hiragino sans gb, "sans-serif", Apple Color Emoji, Segoe UI Emoji, Not Color Emoji;

  /*  for firefox */
  scrollbar-color: theme(colors.gray.300);
  scrollbar-width: thin;
}

html.dark {
  scrollbar-color: theme(colors.muted.500);
}

a,
details summary {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

:root {
  --bg-opacity: rgba(255, 255, 255, 0.72);
}

[data-theme='dark'] {
  --bg-opacity: rgba(29, 29, 31, 0.72);
}

::selection {
  background-color: theme(colors.accent);
  color: theme(colors.white) !important;
  text-shadow: none;
}

[data-theme='dark'] ::selection {
  background-color: theme(colors.accent/0.3);
}

body {
  @apply bg-base-100 text-base-content;
  overflow: overlay;
}

/* Scrollbar Styles */
body::-webkit-scrollbar {
  height: 0;
}

[data-theme='dark'] {
  *::-webkit-scrollbar-thumb {
    border: 3px solid theme(colors.muted.500);
  }

  *::-webkit-scrollbar-thumb:hover {
    background: theme(colors.muted.500/0.5);
  }
}

*::-webkit-scrollbar-thumb {
  background-color: transparent;
  border: 3px solid theme(colors.gray.300);
  @apply rounded-xl;
}

*::-webkit-scrollbar-thumb:hover {
  border-color: theme(colors.muted.500/0.8);
}

*::-webkit-scrollbar {
  width: 6px !important;
  height: 6px !important;
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background: theme(colors.muted.500);
}

*::-webkit-scrollbar-thumb:hover {
  background: theme(colors.muted.500/0.8);
}

*::-webkit-scrollbar-corner {
  background: theme(colors.zinc.100);
}

/* Animation utilities */
.transition-background {
  transition: background-color 0.2s ease-in-out;
}

.animation-blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* View Transitions */
::view-transition-new(root) {
  animation: turnOff 800ms ease-in-out;
}
::view-transition-old(root) {
  animation: none;
}

@keyframes turnOn {
  0% {
    clip-path: polygon(0% 0%, 100% 0, 100% 0, 0 0);
  }
  100% {
    clip-path: polygon(0% 0%, 100% 0, 100% 100%, 0 100%);
  }
}

[data-theme='dark']::view-transition-new(root) {
  animation: turnOn 800ms ease-in-out;
}
::view-transition-old(root) {
  animation: none;
}

@keyframes turnOff {
  0% {
    clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0% 100%);
  }
  100% {
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 0 0);
  }
}
