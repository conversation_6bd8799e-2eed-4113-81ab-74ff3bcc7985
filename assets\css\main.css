@import 'tailwind.css';

/* Global styles */
html {
  font-family: var(--font-sans), system-ui, -apple-system, PingFang SC, "Microsoft YaHei", Segoe UI, Roboto, Helvetica, noto sans sc, hiragino sans gb, "sans-serif", Apple Color Emoji, Segoe UI Emoji, Not Color Emoji;
}

body {
  @apply bg-base-100 text-base-content;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Animation utilities */
.transition-background {
  transition: background-color 0.2s ease-in-out;
}

.animation-blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
