// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/6/28 10:06:41
const configMerger = require("D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/node_modules/.pnpm/@nuxtjs+tailwindcss@6.12.2_magicast@0.3.5/node_modules/@nuxtjs/tailwindcss/dist/runtime/merger.js");

const inlineConfig = {"content":[],"theme":{"extend":{}},"plugins":[]};

const config = [
require("./../tailwind.config.ts")
].reduce((prev, curr) => configMerger(curr, prev), configMerger(inlineConfig, { content: { files: ["D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/plugins/**/*.{js,ts,mjs}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/composables/**/*.{js,ts,mjs}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/utils/**/*.{js,ts,mjs}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","D:/VscodeWorkSpace/deploy-project/nextjs-deploy/Shiro/app.config.{js,ts,mjs}"] } }));

module.exports = config
