kind: pipeline
name: build-and-package
platform:
  os: linux
  arch: amd64

volumes:
  - name: shiro
    host:
      path: /drone/tmp/shiro
  - name: shiro-dist
    host:
      path: /drone/tmp/shiro-dist
  - name: shiro-env
    host:
      path: /home/<USER>/docker-compose/drone/public/shiro/.env

  - name: node_modules_cache
    host:
      path: /drone/tmp/cache
  - name: cache
    host:
      path: /drone/tmp/shiro_next_cache

steps:
  - name: restore-cache
    image: drillster/drone-volume-cache
    settings:
      restore: true
      mount:
        - ./node_modules
    volumes:
      - name: node_modules_cache
        path: /cache

  - name: build
    image: node:20-alpine
    environment:
      NODE_OPTIONS: --max_old_space_size=4096
      NEXT_TELEMETRY_DISABLED: 1
    commands:
      - 'npm i -g pnpm'
      - 'pnpm install --no-frozen-lockfile'
      - 'npm run build:ci'

    depends_on:
      - restore-cache
    volumes:
      - name: shiro-env
        path: /drone/src/.env
      - name: cache
        path: /drone/src/.next

  - name: rebuild-cache
    image: drillster/drone-volume-cache
    settings:
      rebuild: true
      mount:
        - ./node_modules
    volumes:
      - name: node_modules_cache
        path: /cache

    depends_on:
      - build

  - name: package
    image: node:20-alpine
    commands:
      - 'pwd'
      - 'ls -a'
      - 'ls .next'
      - 'apk add zip'
      - 'sh ./standalone-bundle.sh'
    volumes:
      - name: shiro-dist
        path: /drone/src/assets
      - name: cache
        path: /drone/src/.next

    depends_on:
      - build

trigger:
  branch:
    - main
  event:
    include:
      - push
---
kind: pipeline
name: deploy
platform:
  os: linux
  arch: amd64

volumes:
  - name: shiro-dist
    host:
      path: /drone/tmp/shiro-dist

steps:
  - name: transfer file
    image: appleboy/drone-scp
    settings:
      host:
        from_secret: ssh_host
      username:
        from_secret: ssh_username
      key:
        from_secret: ssh_key
      port: 22
      target: /home/<USER>/shiro
      source:
        - assets/release.zip
      rm_target: true
      strip_components: 1
      debug: true
    volumes:
      - name: shiro-dist
        path: /drone/src/assets

  - name: deploy
    image: appleboy/drone-ssh
    settings:
      host:
        from_secret: ssh_host
      username:
        from_secret: ssh_username
      key:
        from_secret: ssh_key
      port: 22
      script:
        - '\npm install --os=linux --cpu=x64 sharp --registry=https://registry.npmmirror.com'
        - cd ~/shiro
        - unzip -o release.zip
        - rm release.zip
        - ls
        - cd standalone
        - cp -r ~/node_modules/sharp ./node_modules
        - ~/.n/bin/pm2 restart ecosystem.config.js
      debug: true
    depends_on:
      - transfer file

depends_on:
  - build-and-package
trigger:
  branch:
    - main
  event:
    include:
      - push
