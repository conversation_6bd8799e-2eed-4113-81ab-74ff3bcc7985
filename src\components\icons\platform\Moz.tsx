import type { SVGProps } from 'react'

export function CibMozilla(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <path
        fill="currentColor"
        d="M0 32V0h32v32zM21.855 9.016l-.308.036l-.292.052l-.281.084l-.265.093l-.271.093l-.249.12l-.24.131l-.229.145l-.224.161l-.213.172l-.203.188l-.183.203l-.177.213l-.172.224l-.145.229l-.147.251l-.135.255l-.12.281l-.104-.297l-.136-.276l-.135-.271l-.156-.249l-.177-.229l-.188-.213l-.197-.199l-.199-.187l-.229-.172l-.224-.147l-.255-.145l-.24-.125l-.265-.104l-.265-.093l-.271-.079l-.276-.068l-.297-.052l-.292-.025l-.291-.032h-.573l-.281.032l-.265.025l-.271.037l-.251.067l-.24.068l-.239.079l-.245.093l-.208.109l-.229.104l-.197.136l-.204.129l-.187.152l-.183.156l-.177.177l-.156.172l-.161.187l-.147.199l-.135.213l-.12.213V9.264H3.532v2.964h1.829v7.812H3.532v3.011h8.443V20.04h-2.64v-5.083l.016-.276l.036-.271l.041-.251l.052-.24l.068-.239l.079-.213l.093-.215l.109-.187l.12-.188l.145-.161l.147-.145l.161-.136l.187-.12l.199-.104l.197-.077l.229-.068l.256-.052l.249-.032l.281-.011h.229l.208.027l.213.041l.188.052l.188.068l.171.079l.163.093l.145.12l.135.135l.131.147l.109.161l.104.183l.084.213l.077.213l.068.255l.052.256l.041.276l.043.307l.011.333v8.12h5.733v-3.011h-1.828v-4.801l.016-.281l.011-.276l.025-.271l.057-.251l.052-.24l.068-.239l.079-.213l.093-.215l.104-.187l.125-.188l.131-.161l.161-.145l.161-.136l.181-.12l.188-.104l.213-.077l.229-.068l.251-.052l.255-.032l.281-.011h.224l.213.027l.213.041l.188.052l.187.068l.172.079l.161.093l.147.12l.135.135l.12.147l.12.161l.104.183l.084.213l.077.213l.068.255l.052.256l.041.276l.037.307l.015.333v8.12h5.735v-3.011h-1.828v-6.296l-.016-.48l-.052-.437l-.077-.427l-.109-.401l-.131-.375l-.161-.344l-.188-.323l-.213-.308l-.239-.265l-.24-.249l-.281-.215l-.276-.203l-.308-.172l-.323-.161l-.333-.12l-.333-.104l-.359-.083l-.344-.052l-.375-.043h-.693z"
      />
    </svg>
  )
}
