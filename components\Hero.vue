<template>
  <section class="hero min-h-[60vh] bg-gradient-to-br from-primary/10 to-secondary/10">
    <div class="hero-content text-center">
      <div class="max-w-md">
        <h1 class="text-5xl font-bold"><PERSON>ro</h1>
        <p class="py-6 text-lg">
          一个现代化的博客，使用 Nuxt.js 构建
        </p>
        <div class="flex gap-4 justify-center">
          <NuxtLink to="/posts" class="btn btn-primary">
            查看文章
          </NuxtLink>
          <NuxtLink to="/notes" class="btn btn-outline">
            阅读笔记
          </NuxtLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Component logic here
</script>

<style scoped>
/* Component specific styles */
</style>
