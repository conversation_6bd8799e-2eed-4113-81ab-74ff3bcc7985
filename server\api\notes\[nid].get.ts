export default defineEventHandler(async (event) => {
  const { nid } = getRouterParams(event)

  // Mock data - replace with real API calls
  const mockNotes = {
    '1': {
      id: '1',
      nid: 1,
      title: '今天学习了 Vue 3 Composition API',
      summary: '记录一下 Vue 3 Composition API 的学习心得',
      content: `# Vue 3 Composition API 学习笔记

今天深入学习了 Vue 3 的 Composition API，感觉这是一个非常强大的功能。

## 什么是 Composition API

Composition API 是 Vue 3 中引入的一套新的 API，它提供了一种更灵活的方式来组织组件逻辑。

## 主要优势

- **更好的逻辑复用**：可以将相关的逻辑组织在一起
- **更好的 TypeScript 支持**：类型推断更加准确
- **更灵活的组织方式**：不再受限于 Options API 的结构

## 基本用法

\`\`\`javascript
import { ref, computed, onMounted } from 'vue'

export default {
  setup() {
    const count = ref(0)
    const doubleCount = computed(() => count.value * 2)
    
    const increment = () => {
      count.value++
    }
    
    onMounted(() => {
      console.log('组件已挂载')
    })
    
    return {
      count,
      doubleCount,
      increment
    }
  }
}
\`\`\`

## 总结

Composition API 让我们能够更好地组织代码，特别是在处理复杂逻辑时。虽然学习曲线稍微陡峭一些，但掌握后会发现它的强大之处。`,
      created: '2024-01-01T00:00:00Z',
      updated: '2024-01-01T00:00:00Z'
    },
    '2': {
      id: '2',
      nid: 2,
      title: '关于代码重构的思考',
      summary: '在重构项目代码时的一些思考和总结',
      content: `# 代码重构思考

最近在重构一个老项目，有一些心得想要记录下来。

## 重构的原则

1. **小步快跑**：不要试图一次性重构所有代码
2. **保持功能不变**：重构过程中不要改变原有功能
3. **测试先行**：确保有足够的测试覆盖

## 重构的步骤

1. 理解现有代码
2. 编写测试
3. 小幅度重构
4. 运行测试
5. 重复上述过程

## 常见的重构技巧

- 提取函数
- 重命名变量
- 消除重复代码
- 简化条件表达式

重构是一个持续的过程，需要耐心和细心。`,
      created: '2024-01-02T00:00:00Z',
      updated: '2024-01-02T00:00:00Z'
    }
  }

  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))

  const note = mockNotes[nid as keyof typeof mockNotes]
  
  if (note) {
    return note
  }

  // Return 404 if note not found
  throw createError({
    statusCode: 404,
    statusMessage: 'Note not found'
  })
})
