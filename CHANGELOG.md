# CHANGELOG

## [1.2.5](https://github.com/Innei/sprightly/compare/v1.2.4...v1.2.5) (2025-01-08)


### Bug Fixes

* type error ([5d01a4d](https://github.com/Innei/sprightly/commit/5d01a4df3db0730563fbf9f1e1f39b715db25732))



## [1.2.4](https://github.com/Innei/sprightly/compare/v1.2.3...v1.2.4) (2025-01-04)


### Bug Fixes

* **deps:** update all non-major dependencies ([#492](https://github.com/Innei/sprightly/issues/492)) ([8ed7b74](https://github.com/Innei/sprightly/commit/8ed7b74e2c10c2dfa9a0bb316b63ebd3b722d0b3))
* **deps:** update all non-major dependencies ([#497](https://github.com/Innei/sprightly/issues/497)) ([0040c37](https://github.com/Innei/sprightly/commit/0040c3719441c4988533e1e160e9057cb56a2b71))
* **deps:** update all non-major dependencies ([#510](https://github.com/Innei/sprightly/issues/510)) ([8efa115](https://github.com/Innei/sprightly/commit/8efa11521a76dd01ba0bd0aab8205de05bbc603c))
* **deps:** update all non-major dependencies ([#511](https://github.com/Innei/sprightly/issues/511)) ([f25e34b](https://github.com/Innei/sprightly/commit/f25e34ba027a87ae19c685fa526b0cbabfcd2f16))
* **deps:** update all non-major dependencies ([#512](https://github.com/Innei/sprightly/issues/512)) ([5b58d6a](https://github.com/Innei/sprightly/commit/5b58d6ab0916dd5a04310308e0fff099bed2391c))
* **deps:** update all non-major dependencies (minor) ([#483](https://github.com/Innei/sprightly/issues/483)) ([50301db](https://github.com/Innei/sprightly/commit/50301dbfc262f00e3e96e7b46db9651ef984f351))
* **deps:** update all non-major dependencies (minor) ([#487](https://github.com/Innei/sprightly/issues/487)) ([589cfb1](https://github.com/Innei/sprightly/commit/589cfb11a98c4fa3652d98a66168c86526a0c863))
* **deps:** update dependency @simplewebauthn/browser to v13 ([#506](https://github.com/Innei/sprightly/issues/506)) ([60589cd](https://github.com/Innei/sprightly/commit/60589cd89525e8a614bf4616b6af4a1ede2b7f32))
* **deps:** update dependency better-auth to v1.1.6 [security] ([#517](https://github.com/Innei/sprightly/issues/517)) ([5edae6f](https://github.com/Innei/sprightly/commit/5edae6fd74c3998424ee77b2c63c7e97e9c28832))
* **deps:** update dependency marked to v15 ([#490](https://github.com/Innei/sprightly/issues/490)) ([e3ce4a8](https://github.com/Innei/sprightly/commit/e3ce4a8f5bec4dc844d2ddccddfaf260ef445fe8))
* **deps:** update dependency nanoid to v5.0.9 [security] ([#508](https://github.com/Innei/sprightly/issues/508)) ([9d23979](https://github.com/Innei/sprightly/commit/9d239798705d641c4033b479d13e13ea3e557802))
* **deps:** update dependency react-error-boundary to v5 ([#515](https://github.com/Innei/sprightly/issues/515)) ([2126042](https://github.com/Innei/sprightly/commit/212604281fad6c24984b9b31a0e9c9d79b626fc6))
* **deps:** update dependency react-router-dom to v7 ([#494](https://github.com/Innei/sprightly/issues/494)) ([04b53f0](https://github.com/Innei/sprightly/commit/04b53f01fde033901042c2cddfa00cc5a98b0e98))
* **deps:** update dependency react-toastify to v11 ([#514](https://github.com/Innei/sprightly/issues/514)) ([1ac4f2f](https://github.com/Innei/sprightly/commit/1ac4f2f038e0ebf27119b8415b65c5063af9e1d0))
* **deps:** update dependency vite to v6 ([#498](https://github.com/Innei/sprightly/issues/498)) ([f754c63](https://github.com/Innei/sprightly/commit/f754c634ffb9ce970a8cac7fc9b9a8d32875ae00))
* link popover transition ([77cb460](https://github.com/Innei/sprightly/commit/77cb4601b03fc4037d0a79dd4d91bcfb4f5098f7))
* provider account id ([b6d777b](https://github.com/Innei/sprightly/commit/b6d777be75c48a5b9e1f0836be2ded286868934a))
* reduce rss cache time, resolves [#516](https://github.com/Innei/sprightly/issues/516) ([80b4460](https://github.com/Innei/sprightly/commit/80b4460f4bd036fd27b2bc0f27dd7d45ed6da78b))
* **sheet:** trigger only in has children ([c3dd574](https://github.com/Innei/sprightly/commit/c3dd574efe4832c8ebd07a968de43e6b463875c8))
* ts types ([b36fda0](https://github.com/Innei/sprightly/commit/b36fda0b0f954fb4e35c7195a06e5247eec6a564))
* update disallow rules in robots metadata ([8a88d2b](https://github.com/Innei/sprightly/commit/8a88d2ba35905efa8ca547047d99594220d4504f))


### Features

* support hash in github link ([#513](https://github.com/Innei/sprightly/issues/513)) ([7cffc4a](https://github.com/Innei/sprightly/commit/7cffc4adf1d3624189000fe74f7798fc3edb2abb))



## [1.2.3](https://github.com/Innei/sprightly/compare/v1.2.2...v1.2.3) (2024-10-27)


### Bug Fixes

* activity icon position ([8f32bff](https://github.com/Innei/sprightly/commit/8f32bffc4d6d606c50158c83f17af08380fe6fd5))
* build ([c6ee7f5](https://github.com/Innei/sprightly/commit/c6ee7f56ddb4d3b9352d5933ee649e408156ef0d))
* **deps:** update all non-major dependencies (minor) ([#475](https://github.com/Innei/sprightly/issues/475)) ([3786f61](https://github.com/Innei/sprightly/commit/3786f611dcba41c9c5ead98ef63c89c98c8722bb))
* downgrade next ([b71fcb6](https://github.com/Innei/sprightly/commit/b71fcb657763602b0b1a9f6aa1e6b0a4b8e2d674))
* header auth ([7a3aab6](https://github.com/Innei/sprightly/commit/7a3aab68086ea60585d32bf849f46e1a36412b85))
* next version ([e8a92d3](https://github.com/Innei/sprightly/commit/e8a92d38274a9a0a8f7a780c27c88c5f52675e3d))



## [1.2.2](https://github.com/Innei/sprightly/compare/v1.2.1...v1.2.2) (2024-10-10)


### Bug Fixes

* build ([0d8b053](https://github.com/Innei/sprightly/commit/0d8b053484ddeed054099de9853bb678fbe554a7))
* **deps:** update all non-major dependencies ([434a47b](https://github.com/Innei/sprightly/commit/434a47b029aa504a574e4ee90d5e1fce21a511c0))
* **deps:** update all non-major dependencies ([#467](https://github.com/Innei/sprightly/issues/467)) ([4c96297](https://github.com/Innei/sprightly/commit/4c962978d19bc6fc11f7f58f46399a20ad0cef87))
* **deps:** update dependency qrcode.react to v4 ([#444](https://github.com/Innei/sprightly/issues/444)) ([a1f96fb](https://github.com/Innei/sprightly/commit/a1f96fb495a80876102c8f3e15ec7bf1a629e81f))


### Features

* add music card to LinkCard (netease/tencent) ([#470](https://github.com/Innei/sprightly/issues/470)) ([2165b6a](https://github.com/Innei/sprightly/commit/2165b6a598a6b464bb85fe0a54f87e939f0e8efb))



## [1.2.1](https://github.com/Innei/sprightly/compare/v1.2.0...v1.2.1) (2024-10-05)


### Bug Fixes

* auto switch legacy comment if no providers ([3fcad20](https://github.com/Innei/sprightly/commit/3fcad2042d16e30040cea04935f5950a92240e33))
* ci ([243886f](https://github.com/Innei/sprightly/commit/243886fc41593a44e1128bfe47ad02b8493dd168))
* **deps:** update all non-major dependencies ([f02f45b](https://github.com/Innei/sprightly/commit/f02f45b2041622c88a8dcdfcf07b53f5a60729c4))
* **deps:** update all non-major dependencies (minor) ([#455](https://github.com/Innei/sprightly/issues/455)) ([79a507b](https://github.com/Innei/sprightly/commit/79a507b4f9b1decd7c3eab196cf7f59e834c4f66))
* **deps:** update all non-major dependencies (minor) ([#460](https://github.com/Innei/sprightly/issues/460)) ([e2e4756](https://github.com/Innei/sprightly/commit/e2e47567c95c8caa5ceef7e25080c8414f45d9a2))
* **deps:** update all non-major dependencies (minor) ([#464](https://github.com/Innei/sprightly/issues/464)) ([c566a33](https://github.com/Innei/sprightly/commit/c566a3387625a44bd08745c7f5a60d9421bd4be7))
* **deps:** update dependency vaul to v1 ([#463](https://github.com/Innei/sprightly/issues/463)) ([36295b4](https://github.com/Innei/sprightly/commit/36295b488f171a3e20c828e03b939cd701086bb4))
* **deps:** update dependency vite to v5.4.6 [security] ([f0037a3](https://github.com/Innei/sprightly/commit/f0037a37c3799425ec864118adcbf0def8bad210))
* import markdown modal ([5b7431f](https://github.com/Innei/sprightly/commit/5b7431fe0bee3b002ca85d71613c565736ef9cd0))
* katex parser ([86c114a](https://github.com/Innei/sprightly/commit/86c114a680bbdbdf1408f74c8d8185d98d73778a))
* next-auth ([715fcc0](https://github.com/Innei/sprightly/commit/715fcc05efc174f9dce836bc2b1d6f15a5217b4e))
* next-auth ([19a28c8](https://github.com/Innei/sprightly/commit/19a28c8bc52bfef8239dec86d8159cf062db7799))


### Features

* add admin url ([a7b9cd7](https://github.com/Innei/sprightly/commit/a7b9cd7375752dc24fa221a47f1b8519d9fe0e2c))
* comment info get from reader ([22492bf](https://github.com/Innei/sprightly/commit/22492bf19a70c534e318fc0239227b606236c95f))
* fetch app url after login ([32a21b4](https://github.com/Innei/sprightly/commit/32a21b44ae0f4f468c0e75741782b9258a694070))



# [1.2.0](https://github.com/Innei/sprightly/compare/v1.1.5...v1.2.0) (2024-09-17)


### Bug Fixes

* add default two line breaks per line break in comment Textarea ([db4a859](https://github.com/Innei/sprightly/commit/db4a8590aec257d6e434967e8a3b0a661c096c30)), closes [#410](https://github.com/Innei/sprightly/issues/410) [#411](https://github.com/Innei/sprightly/issues/411) [#410](https://github.com/Innei/sprightly/issues/410)
* **deps:** update all non-major dependencies ([055ba6f](https://github.com/Innei/sprightly/commit/055ba6fe577b1d63b10e00ed3395e4daa46997aa))
* **deps:** update all non-major dependencies ([3004eff](https://github.com/Innei/sprightly/commit/3004eff495ea097063bb6fb1cf37f55b3af45355))
* **deps:** update all non-major dependencies ([#405](https://github.com/Innei/sprightly/issues/405)) ([46b6c08](https://github.com/Innei/sprightly/commit/46b6c087f43f1c5ab2f523dc7a2ece538545e5e4))
* **deps:** update all non-major dependencies ([#424](https://github.com/Innei/sprightly/issues/424)) ([952dbea](https://github.com/Innei/sprightly/commit/952dbeac5f0a87dad2c6399c323959b2596192a4))
* **deps:** update all non-major dependencies (minor) ([#396](https://github.com/Innei/sprightly/issues/396)) ([ed30e11](https://github.com/Innei/sprightly/commit/ed30e11ae94176bbdbdff3ea0f2d2679da98e3e8))
* **deps:** update all non-major dependencies (minor) ([#403](https://github.com/Innei/sprightly/issues/403)) ([90b6a32](https://github.com/Innei/sprightly/commit/90b6a32785a7aac1b0a2a14e9701b304af7ce2fc))
* **deps:** update all non-major dependencies (minor) ([#406](https://github.com/Innei/sprightly/issues/406)) ([36d4d69](https://github.com/Innei/sprightly/commit/36d4d691a828075a7e5eb6383253c88e3a65e7bd))
* **deps:** update all non-major dependencies (minor) ([#413](https://github.com/Innei/sprightly/issues/413)) ([9a8ee6e](https://github.com/Innei/sprightly/commit/9a8ee6ebfae4185067aad879619c87de3070ab2b))
* **deps:** update all non-major dependencies (minor) ([#418](https://github.com/Innei/sprightly/issues/418)) ([9fdcddf](https://github.com/Innei/sprightly/commit/9fdcddf1a45cec2d77e05193407ce228b468ee8a))
* **deps:** update all non-major dependencies (minor) ([#420](https://github.com/Innei/sprightly/issues/420)) ([399b2b0](https://github.com/Innei/sprightly/commit/399b2b09461b251ed79b099d217129773a8c705c))
* **deps:** update all non-major dependencies (minor) ([#447](https://github.com/Innei/sprightly/issues/447)) ([ada2d48](https://github.com/Innei/sprightly/commit/ada2d48b131bbff525168711a4bfc845d8061f2b))
* **deps:** update all non-major dependencies (patch) ([#409](https://github.com/Innei/sprightly/issues/409)) ([13ed6b7](https://github.com/Innei/sprightly/commit/13ed6b77cfe3a3a3954c0d24c45f8ecf2c2ab12a))
* **deps:** update all non-major dependencies (patch) ([#412](https://github.com/Innei/sprightly/issues/412)) ([a74ae5c](https://github.com/Innei/sprightly/commit/a74ae5cee7b68fc88a98b2cc5982805b458bb0e6))
* **deps:** update all non-major dependencies (patch) ([#415](https://github.com/Innei/sprightly/issues/415)) ([3c1cdbb](https://github.com/Innei/sprightly/commit/3c1cdbb3e209eb8b2e3f9236fca27ed70672c113))
* **deps:** update all non-major dependencies (patch) ([#417](https://github.com/Innei/sprightly/issues/417)) ([73730d6](https://github.com/Innei/sprightly/commit/73730d6b22180540a1435fba6744b5a72f255fa4))
* **deps:** update dependency chroma-js to v3 ([#432](https://github.com/Innei/sprightly/issues/432)) ([f7ab7e7](https://github.com/Innei/sprightly/commit/f7ab7e7a0b1ebaafa10acb35110f57512a783ecb)), closes [#431](https://github.com/Innei/sprightly/issues/431)
* **deps:** update dependency marked to v14 ([#419](https://github.com/Innei/sprightly/issues/419)) ([7dc7e1d](https://github.com/Innei/sprightly/commit/7dc7e1de292be7cd6279bcb7b18c5f4856c6a101))
* **deps:** update dependency mermaid to v11 ([#442](https://github.com/Innei/sprightly/issues/442)) ([2590ab0](https://github.com/Innei/sprightly/commit/2590ab06ce79990c1eb0e7345681b1b855b68320))
* **deps:** update dependency openai to v4.61.0 ([#453](https://github.com/Innei/sprightly/issues/453)) ([98710de](https://github.com/Innei/sprightly/commit/98710de863028d3d596ece415a3e941ca7c78521))
* disable prettier sort ([1eb5a68](https://github.com/Innei/sprightly/commit/1eb5a68232ff93c31fe64e0072ffa6f0d3c6a6ec))
* emoji picker style ([1e896f2](https://github.com/Innei/sprightly/commit/1e896f2197d5c45d57b3d324c4e2004a0e64c29a))
* feed image ([a2455d7](https://github.com/Innei/sprightly/commit/a2455d78f1d605c244af65060f8f5408dba26cfb))
* home hero styles ([c1c0066](https://github.com/Innei/sprightly/commit/c1c0066f6a93d297606abad20ce75613342d87f7))
* kbd color in dark mode ([5e7df37](https://github.com/Innei/sprightly/commit/5e7df376247c386cec249793f6768b0326cafa12))
* leetcode api proxy ([0649f97](https://github.com/Innei/sprightly/commit/0649f97242c3c1979b86577283c67dcbb612f8c1))
* link card style ([e9d7aab](https://github.com/Innei/sprightly/commit/e9d7aabc3c03f6e4c9a7b1498769845cf4977494))
* link underline style when selected ([fa7fb07](https://github.com/Innei/sprightly/commit/fa7fb074c1462d013455b5b9995e5d91e1cbc517))
* note paragraph style ([c50113c](https://github.com/Innei/sprightly/commit/c50113c48362793eea38a3e9b4a2b8b98b8b04ea))
* persist typing ([6031d85](https://github.com/Innei/sprightly/commit/6031d85074d81d603f6664d8cf2401bea1d4af14))
* project list style ([f4d66cf](https://github.com/Innei/sprightly/commit/f4d66cf2497f94d8e57355d84a3f7724ebf407e4))
* project style ([46e10f7](https://github.com/Innei/sprightly/commit/46e10f7e0fa1eee1cbf7510212f881a96ddc2b64))
* slug replacer ([d3f7f99](https://github.com/Innei/sprightly/commit/d3f7f99503d17a1214c895a03493928fa65ac277))
* social link ([4e26b42](https://github.com/Innei/sprightly/commit/4e26b429f61bd89be883c2211e4ca853fbd48e58))
* storybook ([a995ff8](https://github.com/Innei/sprightly/commit/a995ff8392b632f520e7e8511d24ebb33f020561))
* table overflow x fixes [#428](https://github.com/Innei/sprightly/issues/428) ([2878220](https://github.com/Innei/sprightly/commit/2878220cbf8c6993cf1dc194d8697c7098711de9))
* toc fab sheet close transition ([94aa73e](https://github.com/Innei/sprightly/commit/94aa73e2829b365ff17106b585eaa5ec052744aa))
* toc scroller and mask ([9b70e9b](https://github.com/Innei/sprightly/commit/9b70e9bef89d3f9cdc526c67629b9ac10be18a92))
* typing ([b1f9ee2](https://github.com/Innei/sprightly/commit/b1f9ee2d3c272a6272b5206c74b28efe47acbc16))
* when comand pressed, skip peek, fixed  Innei/Shiro[#436](https://github.com/Innei/sprightly/issues/436) ([27b0fdc](https://github.com/Innei/sprightly/commit/27b0fdca0d3e7dfa19e7c795266f47cd598709bb))
* wrapped element `x,y` detection ([8e37913](https://github.com/Innei/sprightly/commit/8e37913eee703b4a23bc71610e23eb48b0269f7a))
* wrapped element calcation size and position ([86a702c](https://github.com/Innei/sprightly/commit/86a702cc19c331d892bb62e818054a0c6f7b7cc3))


### Features

* add fetchLeetCodeQuestionData function to LinkCard ([#439](https://github.com/Innei/sprightly/issues/439)) ([7df7448](https://github.com/Innei/sprightly/commit/7df74485ccbf880856dca881ac862971bd8cabe6)), closes [#431](https://github.com/Innei/sprightly/issues/431) [#427](https://github.com/Innei/sprightly/issues/427)
* make oauth great again! ([ee0fc7a](https://github.com/Innei/sprightly/commit/ee0fc7a8c3f33e5ea4af1db2d0e0cd83b32d75d0))
* render single link style in thinking ([1581b69](https://github.com/Innei/sprightly/commit/1581b698cc3eb804c780f5ab2321f583d48f1765))
* scroll mask ([00a6921](https://github.com/Innei/sprightly/commit/00a692141028dc6bd25da602f807a093b4ee77f3))
* update spring scroll preset ([d665858](https://github.com/Innei/sprightly/commit/d6658580877cd6d1c89d555d4f6196b9ea8ca0ab))



## [1.1.5](https://github.com/Innei/sprightly/compare/v1.1.4...v1.1.5) (2024-07-06)


### Bug Fixes

* authed input name getter ([2bae0e7](https://github.com/Innei/sprightly/commit/2bae0e794c8fb0b13563273a05e8c4303cf1ba11))
* **deps:** update all non-major dependencies ([ba98daf](https://github.com/Innei/sprightly/commit/ba98daf63f5b8b9a603bda3355ea776ecd452347))
* **deps:** update all non-major dependencies ([#380](https://github.com/Innei/sprightly/issues/380)) ([46a9a57](https://github.com/Innei/sprightly/commit/46a9a57de175a7c53a307a2debe24bcd67144335))
* **deps:** update all non-major dependencies (minor) ([#386](https://github.com/Innei/sprightly/issues/386)) ([1365704](https://github.com/Innei/sprightly/commit/1365704e96037dd26da863e396b84917781668c4))
* **deps:** update all non-major dependencies (minor) ([#389](https://github.com/Innei/sprightly/issues/389)) ([6b5075f](https://github.com/Innei/sprightly/commit/6b5075ffffdcd729900207430efcde9391a36bfa))
* **deps:** update all non-major dependencies (minor) ([#391](https://github.com/Innei/sprightly/issues/391)) ([9a44f93](https://github.com/Innei/sprightly/commit/9a44f93248a3b61df8d3c6b5cd64e2fb3d1ee2c0))
* **deps:** update all non-major dependencies (minor) ([#392](https://github.com/Innei/sprightly/issues/392)) ([913cddd](https://github.com/Innei/sprightly/commit/913cddd7c8a5f7ec5b5421ca0fd5df8d98462767))
* **deps:** update all non-major dependencies (patch) ([#385](https://github.com/Innei/sprightly/issues/385)) ([cb9264f](https://github.com/Innei/sprightly/commit/cb9264f5f3715943a46f308423727d9aacd2953f))
* **deps:** update dependency marked to v13 ([#384](https://github.com/Innei/sprightly/issues/384)) ([9625774](https://github.com/Innei/sprightly/commit/9625774d26ba29035fbfb4f320b2fab1b7a8fda7))
* feed language ([ce7bc0d](https://github.com/Innei/sprightly/commit/ce7bc0d7fe486a58d11d74a6b6b185ebdb249446))
* feed link ([6d354c2](https://github.com/Innei/sprightly/commit/6d354c24080d8d4785201a7b968a13154ff98560))
* framer modal enter motion ([c5f0a59](https://github.com/Innei/sprightly/commit/c5f0a59ffedd2fefe757eea49d1abb9354231db4))
* list p style ([c3f5c79](https://github.com/Innei/sprightly/commit/c3f5c793a3e845e81356c7c2e8065e8946adbe50))
* remove katex in toc tree ([0097e9f](https://github.com/Innei/sprightly/commit/0097e9fe6440714f788fcafe103d7e7261a15537))
* remvoe panel clickaway ([434efee](https://github.com/Innei/sprightly/commit/434efee18b86c700c2e6d6088896718eb36c137f))
* scrollbar color, fixes [#390](https://github.com/Innei/sprightly/issues/390) ([3fa97c3](https://github.com/Innei/sprightly/commit/3fa97c3ea6a593d57b3224d157d9cad62cf073a0))
* tag detail modal loading ([2a5621b](https://github.com/Innei/sprightly/commit/2a5621b5b006b791e6b87a11559252d7721b44ed))
* type ([a6471df](https://github.com/Innei/sprightly/commit/a6471df65f0fe9d0d495eda5cf04a65bd55b5dbb))


### Features

* footnote style and gfm task style ([fafd9e4](https://github.com/Innei/sprightly/commit/fafd9e4499e9929c529336fb777c5c6501a9fe31))



## [1.1.4](https://github.com/Innei/sprightly/compare/v1.1.3...v1.1.4) (2024-06-11)


### Bug Fixes

* ci ([3431ac8](https://github.com/Innei/sprightly/commit/3431ac8292e84cca6fa107b904ad8a553fcb2f8b))
* **deps:** update all non-major dependencies (minor) ([#368](https://github.com/Innei/sprightly/issues/368)) ([f0679ae](https://github.com/Innei/sprightly/commit/f0679ae56f3679237009ea94ef4f6c4a926a191b))
* jotai path configuration on windows ([#373](https://github.com/Innei/sprightly/issues/373)) ([fec6f9b](https://github.com/Innei/sprightly/commit/fec6f9be035fdad59249f09cf9e025ba14506b89))
* katex deps ([#375](https://github.com/Innei/sprightly/issues/375)) ([8209bb0](https://github.com/Innei/sprightly/commit/8209bb0c9971a558f5350192be2a54735cd57826))
* link card github pr parser ([2e5b9aa](https://github.com/Innei/sprightly/commit/2e5b9aa4a90be105528ee22ee836d01a9732c1c0))
* markdown first line of list ([51a10f1](https://github.com/Innei/sprightly/commit/51a10f1c256d38c94a9fb796a222b4f8124128be))
* markdown render error ([653cac2](https://github.com/Innei/sprightly/commit/653cac2ee21b7ddb9f9fdc4ab1c4d53cb511db4e))
* metabar reading count ([13917d8](https://github.com/Innei/sprightly/commit/13917d86a99fbf5b4fd9505c9f7badfd102b1d47))
* preview page ([2bc2500](https://github.com/Innei/sprightly/commit/2bc250080f1b2f4fd8e2ffe5a47a3ff70633a890))
* rss render ([4825aef](https://github.com/Innei/sprightly/commit/4825aefc7948cee5de8940b4958401a2b2b88dfc))
* xlog summary, closes [#24](https://github.com/Innei/sprightly/issues/24) ([3a7e297](https://github.com/Innei/sprightly/commit/3a7e297fb979dbf7f509d9082bc31799b5633950))



## [1.1.3](https://github.com/Innei/sprightly/compare/v1.1.2...v1.1.3) (2024-05-31)


### Bug Fixes

* article html struture ([609299a](https://github.com/Innei/sprightly/commit/609299a0d75ea407eb36847ddc5284ff0b55609f))
* attach final header ([3e166f8](https://github.com/Innei/sprightly/commit/3e166f8030fbf713b8d2df29321bf2e4a96318f0))
* call `notfound` if has no any notes ([23bff57](https://github.com/Innei/sprightly/commit/23bff57b3ff0bcfc8a2ef7a8d9de931b42972dfe))
* ci build ([7f23e52](https://github.com/Innei/sprightly/commit/7f23e5290536483cc25909e74a243052bd6dfdcd))
* deps ([a2df050](https://github.com/Innei/sprightly/commit/a2df05060f9206399c366b1512e343c952a74d8e))
* **deps:** update all non-major dependencies ([6fb964a](https://github.com/Innei/sprightly/commit/6fb964a7fd291f5645136a57b01f47033c76e2fb))
* **deps:** update all non-major dependencies ([5fec3d2](https://github.com/Innei/sprightly/commit/5fec3d27d854cb3aefc321fd22e95d223e0ae001))
* **deps:** update all non-major dependencies (minor) ([#360](https://github.com/Innei/sprightly/issues/360)) ([7a36109](https://github.com/Innei/sprightly/commit/7a36109431e4667f3a8e75679aaa168dd3732e9b))
* **deps:** update all non-major dependencies (minor) ([#364](https://github.com/Innei/sprightly/issues/364)) ([4c6da75](https://github.com/Innei/sprightly/commit/4c6da75a2bed49418dc1448f2f03206e1c47ea5d))
* **deps:** update dependency use-context-selector to v2 ([#353](https://github.com/Innei/sprightly/issues/353)) ([71f5329](https://github.com/Innei/sprightly/commit/71f532967bdb799881330b1cbf2ad2074a812218))
* friend section style ([df98c4f](https://github.com/Innei/sprightly/commit/df98c4fed4f426f13a7ea12ed37f15a7718ceddb))
* header meta bar style ([4dd56bb](https://github.com/Innei/sprightly/commit/4dd56bb160a5033e4d5c79c1c39cec493f82bf9d))
* home og container fixed size ([13468e0](https://github.com/Innei/sprightly/commit/13468e09cc871f4dab52c917d0256b6763fad953))
* not optmize for gif ([56d5c25](https://github.com/Innei/sprightly/commit/56d5c25809ab832328f8809603a3eb06a8eb1e65))
* note edit button position ([762083c](https://github.com/Innei/sprightly/commit/762083c6b823fa62f42550d873cf4f65262adb78))
* remove og title style ([9314ff8](https://github.com/Innei/sprightly/commit/9314ff8cf7df307b9548145966bff0b1d647120f))
* shiki copy code button pos ([fa68aca](https://github.com/Innei/sprightly/commit/fa68acabf3a255b73ecfefc2bd6f327933aa002d))
* styles ([3ab82a2](https://github.com/Innei/sprightly/commit/3ab82a26c1264aea1a93667b6f54e99e6d1a9099))
* tooltip style ([ab0ab97](https://github.com/Innei/sprightly/commit/ab0ab97817b6763df2075208c6dbcabdabf11058))
* try fix ci ([9823a00](https://github.com/Innei/sprightly/commit/9823a00d75a0bfda6a6ff93d256275f0f53ee7a3))
* try fix pnpm version ([853fda7](https://github.com/Innei/sprightly/commit/853fda728013d3f15e325ae54e27664c8d50d6f6))


### Features

* comment oauth set url ([54eaf32](https://github.com/Innei/sprightly/commit/54eaf327dc499d339338dc6c73e8c539e8efd9ca))
* new copy code animate ([44033bf](https://github.com/Innei/sprightly/commit/44033bfd2a8b60e4d161030ee6e7a67f6f07c383))



## [1.1.2](https://github.com/Innei/sprightly/compare/v1.1.1...v1.1.2) (2024-05-04)


### Bug Fixes

* activity card like type render ([38655f2](https://github.com/Innei/sprightly/commit/38655f28b1828f5e5c55f174b15e1d706df14f7b))
* clerk style ([33f0669](https://github.com/Innei/sprightly/commit/33f0669fb24576b11580014816254c103b794fe9))
* comment markdown style ([03bc7b1](https://github.com/Innei/sprightly/commit/03bc7b11d6183f40b66e74d773e490a4ca79f94e))
* comment transition ([a2bf130](https://github.com/Innei/sprightly/commit/a2bf1308c470461bba25e2094c586a458d6e09d8))
* enable middleware to get search in layout ([cd4ee10](https://github.com/Innei/sprightly/commit/cd4ee10746302c7100496e01d46032a9b49d0365))
* framer motion bg layout ([1643f95](https://github.com/Innei/sprightly/commit/1643f95c2145220ad27fe3db40d40c2476ea395c))
* hero text pos ([b9608de](https://github.com/Innei/sprightly/commit/b9608de4de31e4e5bc138a103478146d929bd8c6))
* modal title bar ([17d467c](https://github.com/Innei/sprightly/commit/17d467cdb2b6a2f04bf3bef9f4e988d632a263db))
* og cjk char width calc ([3f193a4](https://github.com/Innei/sprightly/commit/3f193a41b2a798925f3ee67e2b4c99435cb49329))
* optimize comment style ([c1a738b](https://github.com/Innei/sprightly/commit/c1a738b187008ce8d0f575a973f2060cdac5c6fd))
* shiki highlight jumping ([919c4c1](https://github.com/Innei/sprightly/commit/919c4c1258d665667fbaed4d305ab8f92379e454))
* some styles ([84cea05](https://github.com/Innei/sprightly/commit/84cea0591bff8da5026e8e969629f88a69f0cbf4))
* type error ([b04194c](https://github.com/Innei/sprightly/commit/b04194c72f483178b2354b23e76053f003aec014))


### Features

* shiki block `expand` attr ([f6b0d1d](https://github.com/Innei/sprightly/commit/f6b0d1d2066a4598da5b77b71fb0326a72e3720d))



## [1.1.1](https://github.com/Innei/sprightly/compare/v1.1.0...v1.1.1) (2024-04-28)


### Bug Fixes

* [#346](https://github.com/Innei/sprightly/issues/346) ([ab359f5](https://github.com/Innei/sprightly/commit/ab359f577f116de97fc4346e1072357ce08ed0cf))
* **deps:** update all non-major dependencies ([279fbb5](https://github.com/Innei/sprightly/commit/279fbb56c0a5b37047811e0dcead3501a7f9c9ca))
* **deps:** update all non-major dependencies ([#310](https://github.com/Innei/sprightly/issues/310)) ([a9e0674](https://github.com/Innei/sprightly/commit/a9e067433a5197f08d5a53e9c25930a3d5e076e3))
* donate qrcode map ([#347](https://github.com/Innei/sprightly/issues/347)) ([005485c](https://github.com/Innei/sprightly/commit/005485cc8b09b28b91293938176f99567f2316c5))
* header logo style in mobile ([e21ffad](https://github.com/Innei/sprightly/commit/e21ffadbce0ae7fc768bef0bab350782be049c8d))
* help icon style ([528cb9b](https://github.com/Innei/sprightly/commit/528cb9baf61323101e549da7d1fe109745ce51b3))
* home activity ref type link ([0bb00bd](https://github.com/Innei/sprightly/commit/0bb00bdf365c439b83c57da4ec8d02e7703aa9ce))
* home page layout center ([a6f5533](https://github.com/Innei/sprightly/commit/a6f5533f70714dd482bf96be0568d735228193b1))
* home screen style, fix [#16](https://github.com/Innei/sprightly/issues/16) ([78a59fa](https://github.com/Innei/sprightly/commit/78a59fa8118ebb6bf03a50119dee9eb3c48fc87a))
* hydration error ([0132723](https://github.com/Innei/sprightly/commit/0132723cbbba5d27c4b0e9ba569818cf12cd362b))
* import statement in next.config.mjs in nodejs version ([9eb603c](https://github.com/Innei/sprightly/commit/9eb603c94e4c7f032e6791f929432b68d8ff7ed5))
* katex regexp ([810deb4](https://github.com/Innei/sprightly/commit/810deb4a27ab09dcccd85e5099811a9e3b0b6ccd))
* link parser ([3b7fcfd](https://github.com/Innei/sprightly/commit/3b7fcfda38e94baa506acea0fcc372fe91898768))
* lint ([44f7151](https://github.com/Innei/sprightly/commit/44f7151ebebb868957fed10e95040125c5ba8788))
* not-found route segment ([5b73dc9](https://github.com/Innei/sprightly/commit/5b73dc9468ad4459351ca03d3981ca998c8c104f))
* note root banner style, closes [#13](https://github.com/Innei/sprightly/issues/13) ([70e282c](https://github.com/Innei/sprightly/commit/70e282c02ff78fc04914d0a57c652f36a144bcb1))
* pm2 config ([3478e54](https://github.com/Innei/sprightly/commit/3478e54c908b0fd59389e16efe78de08ca02d6d1))
* reduce shiki bundle ([2b7f89f](https://github.com/Innei/sprightly/commit/2b7f89f6a3e496594ac30b0b1d5840ef74cfe2ed))
* remove `Supsense` in note page detail ([3d5a773](https://github.com/Innei/sprightly/commit/3d5a773e30db3853fb0569979b857934221ff389))
* s3 config ([185b5d5](https://github.com/Innei/sprightly/commit/185b5d5ca85029bf6f8bfb951e6f06d5eb10f780))
* shiki code block cls and lcp ([d9dfd41](https://github.com/Innei/sprightly/commit/d9dfd411246a29966ce197008fd1b96fde9186b4))
* storybook ([de67097](https://github.com/Innei/sprightly/commit/de670975262c9a8d7f1f89d5796f36ded5207f72))
* toast style ([6f75a2e](https://github.com/Innei/sprightly/commit/6f75a2e66cfaf669c0762d6b478dee7e18ecfb8d))
* type error ([e3bfebf](https://github.com/Innei/sprightly/commit/e3bfebf8b18775cdf926f54335771e5d7387d18b))
* typing error ([280fe3d](https://github.com/Innei/sprightly/commit/280fe3d60029ff9553a79591988a3555f0ea88a7))


### Features

* add `popper` for `MLink` ([2bcf16c](https://github.com/Innei/sprightly/commit/2bcf16cf40a0a788ecdf896c6c8c34e55b23f5e3))
* new comment observer ([d8595b8](https://github.com/Innei/sprightly/commit/d8595b85384b9e1c67cc629258bb8c7e2046276e))
* optimize popover in mobile ([83771f2](https://github.com/Innei/sprightly/commit/83771f295b0c4c62e6308779dedcfe316c9e839e))
* self favicon in `MLink` ([4aac9b8](https://github.com/Innei/sprightly/commit/4aac9b8c66445f1ceb36dbd0398dcdf4e63cc1dc))
* shiki fully support ([42e21cc](https://github.com/Innei/sprightly/commit/42e21cc9df5ae73ffd6d65c7152f631a92e8cd74))



# [1.1.0](https://github.com/Innei/sprightly/compare/v1.0.3...v1.1.0) (2024-04-17)


### Bug Fixes

* add `peek` to global ([cfe4195](https://github.com/Innei/sprightly/commit/cfe4195f7ef72b4fdf5a96171ad511f7a124e372))
* add missing props ([0501f62](https://github.com/Innei/sprightly/commit/0501f627dbb9c830bf5f10a4a60558519d9a7cc3))
* artifact ([6545e41](https://github.com/Innei/sprightly/commit/6545e41fb62875ec11ba67337015cecb50406dc0))
* build script ([16b43fb](https://github.com/Innei/sprightly/commit/16b43fb21c62a29f616f93ab55ff6ebc96ebd28c))
* clerk card padding top ([#333](https://github.com/Innei/sprightly/issues/333)) ([3512f53](https://github.com/Innei/sprightly/commit/3512f53cbf2b48f6fd2d03363d29b63a38d0f071))
* deps ([4b7cc63](https://github.com/Innei/sprightly/commit/4b7cc6371b20b19eedf43cd1fcb3377a74b85e49))
* **deps:** update all non-major dependencies ([7fce5c4](https://github.com/Innei/sprightly/commit/7fce5c42167762d76e8fc858a52c21b7bc5adea3))
* **deps:** update all non-major dependencies ([1fb5a0f](https://github.com/Innei/sprightly/commit/1fb5a0f7a12d6411b82af4044e7228d44fd2ea35))
* **deps:** update dependency vite to v5.2.6 [security] ([659fdb7](https://github.com/Innei/sprightly/commit/659fdb773392ab04a4407aa4e9844e0cb13956c6))
* dom nodes increase ([5d90266](https://github.com/Innei/sprightly/commit/5d902663de17e854a6244df198fd2d7598a3e7db))
* git remote url ([6de0109](https://github.com/Innei/sprightly/commit/6de010999a6c031323cd287e1d0928d32290317e))
* if access hidden note when loggin ([787c7e7](https://github.com/Innei/sprightly/commit/787c7e7ed97392dcce07dd914b435f246f11b0cb))
* **md:** container style ([70884aa](https://github.com/Innei/sprightly/commit/70884aa4b0f9d6ddb29a6dfdab624fffb1381ea6))
* modal present get scroll top ([e1ec6c9](https://github.com/Innei/sprightly/commit/e1ec6c913e0bc5b8640364899d7d7a9b31fc02c1))
* note head cover cls ([cc75e1f](https://github.com/Innei/sprightly/commit/cc75e1f22c68995ba2af4331a4125e7729692e89))
* note password access ([cfd3cbc](https://github.com/Innei/sprightly/commit/cfd3cbc68cbdf66f33520b3e7c46848d325db320))
* note timeline nav to hide note should accessible if logged ([6412e28](https://github.com/Innei/sprightly/commit/6412e28adfc82c33cad3d881e974e25bf3092352))
* popover ([cb17b83](https://github.com/Innei/sprightly/commit/cb17b83a6a29245868e66e7e7834faab77427e41))
* post related blocl style ([e52cb55](https://github.com/Innei/sprightly/commit/e52cb55101c86785fb008850c516b6c02e93cefe))
* request ([c901dbb](https://github.com/Innei/sprightly/commit/c901dbbdc5d914a548e6ceee99e4b1dbbfb0c841))
* selection bg in dark mode ([b9390c5](https://github.com/Innei/sprightly/commit/b9390c57ba6306795e622ec113c132ff798900a3))
* server fetch lastest note ([790d934](https://github.com/Innei/sprightly/commit/790d9342856e742197165a820949be6ce8894f2d))
* SharedWorker not working on some mobile browsers ([#336](https://github.com/Innei/sprightly/issues/336)) ([c549a2f](https://github.com/Innei/sprightly/commit/c549a2fcbe38ab701bcb55c9ce5a4536fb720778))
* shiki dynamic render ([0bff0db](https://github.com/Innei/sprightly/commit/0bff0db0e4bad150ccc056cf1bc80bda3fdbc95d))
* type error ([ca04806](https://github.com/Innei/sprightly/commit/ca0480649813e9d47661fa93009f50b328fa3b36))
* use client ([e39a1c9](https://github.com/Innei/sprightly/commit/e39a1c9a6a7f8d628e943542cbc671c58afba50d))
* wrap home page ([158f30b](https://github.com/Innei/sprightly/commit/158f30bafc07cedc92ced95dda1e5855fa1ad96f))


### Features

* home page redesign ([5630729](https://github.com/Innei/sprightly/commit/5630729bd8820a783c31bd44dc42915912d3aca4))
* upgrade nextjs 14.2 ([4c377eb](https://github.com/Innei/sprightly/commit/4c377eb3d4ac4eb909dccdd8b18a69d8fa2de9ea))
* worker socket ([#3](https://github.com/Innei/sprightly/issues/3)) ([550abd7](https://github.com/Innei/sprightly/commit/550abd72508d9b586342eeae3b706ad7a8bc35ae))



## [1.0.3](https://github.com/Innei/sprightly/compare/v1.0.2...v1.0.3) (2024-04-04)


### Bug Fixes

* back top ([70e8437](https://github.com/Innei/sprightly/commit/70e8437ad5b10ed729edcf5d329cc124e383e108))
* broswer support check ([#327](https://github.com/Innei/sprightly/issues/327)) ([a413e6e](https://github.com/Innei/sprightly/commit/a413e6eb4b64bd8c87e4a56e2d65b25dfd5f0286))
* cc ([89b136a](https://github.com/Innei/sprightly/commit/89b136a8332d33deb0335c87f9cc51157b130d3d))
* comment toast ([d9fd460](https://github.com/Innei/sprightly/commit/d9fd460dc6af8c809b1acd800ef7c42365bf6afd))
* css ([301a5f2](https://github.com/Innei/sprightly/commit/301a5f2ffe576e53cea1908edf68e27d912919e7))
* donate side button motion ([acbb77d](https://github.com/Innei/sprightly/commit/acbb77d6ac3199daefc4adf22c72df6468d190c5))
* footer GatewayInfo divider ([#328](https://github.com/Innei/sprightly/issues/328)) ([15c92d9](https://github.com/Innei/sprightly/commit/15c92d9ad874167b1e12d3c483ed0cd900abe5fa))
* form ref getter ([4257ba1](https://github.com/Innei/sprightly/commit/4257ba13013c8808b7d74e0c352c6f70755b10e3))
* gallery and text indent style ([53b8b91](https://github.com/Innei/sprightly/commit/53b8b915e52d1ecb281710be56cdcca938afc8c9))
* katex overflow ([42c4e41](https://github.com/Innei/sprightly/commit/42c4e41bbbdcabd650f0417deec7ce5f583e0b74))
* lockfile ([cb116f9](https://github.com/Innei/sprightly/commit/cb116f9522b1de448cd42517f92c3df0331cdf1d))
* prose style ([0c06c2d](https://github.com/Innei/sprightly/commit/0c06c2d0125390dd8ce5fbf32e177def359c52ae))
* say data fetching and masonry layout ([82dd584](https://github.com/Innei/sprightly/commit/82dd5842c276303d8c3cd2651fc44750ef871dc2))
* script inject ([50c200b](https://github.com/Innei/sprightly/commit/50c200bac22a56ab068974642721b0571a4e4736))
* sync server time ([fb167c5](https://github.com/Innei/sprightly/commit/fb167c53e3e0243d72d21350ccc15433a8e4aecc))
* textarea style ([958df38](https://github.com/Innei/sprightly/commit/958df38580c5faba13e52593fe19e3035b68ac41))
* thinking query offset ([bb3dbf4](https://github.com/Innei/sprightly/commit/bb3dbf4f08f6f5364e174dfbf80991c37ea2cf20))
* toc auto scroll ([b59f029](https://github.com/Innei/sprightly/commit/b59f0291b125dfd07225a37a7ee817822ceb8668))
* vite build ([9bc80c1](https://github.com/Innei/sprightly/commit/9bc80c10ce05128af68adbc30ba36acebd9c0e74))


### Features

* add version hash ([143d646](https://github.com/Innei/sprightly/commit/143d646263aa5beb9aef88ebea6a90c79ab2ae54))
* bilibili video render ([21954c7](https://github.com/Innei/sprightly/commit/21954c7f0973a7b3439e07970ba6984ccbfb73c1))
* hide module when no data ([#322](https://github.com/Innei/sprightly/issues/322)) ([c878b8f](https://github.com/Innei/sprightly/commit/c878b8f5c262b89517e02791d143d6019646f298))



## [1.0.2](https://github.com/Innei/sprightly/compare/v1.0.1...v1.0.2) (2024-03-29)


### Bug Fixes

* docker ci ([b1a64f5](https://github.com/Innei/sprightly/commit/b1a64f5da2b87863032feb50b016a91951e431f6))



## [1.0.1](https://github.com/Innei/sprightly/compare/v1.0.0...v1.0.1) (2024-03-29)


### Bug Fixes

*  Excalidraw component styling ([d1529c5](https://github.com/Innei/sprightly/commit/d1529c506b894953be4bd50d85db738cb905c520))
* `FavoriteSection` style ([d89cb1f](https://github.com/Innei/sprightly/commit/d89cb1f8b00a18c099fd06db32a1f4dd27f7c7e5))
* accurate image size calculation ([184f652](https://github.com/Innei/sprightly/commit/184f6520c29c354e674f94d653afd10173e7c9b9))
* activity icon fallback ([f07f390](https://github.com/Innei/sprightly/commit/f07f3902e2c9516c9f4ffdbf01366680ab83e66a))
* add `Tabs` support ([04f989a](https://github.com/Innei/sprightly/commit/04f989ae0d86568b43cad7400960097c0080a1ce))
* add default `nid`for note dto ([a4143ff](https://github.com/Innei/sprightly/commit/a4143fff19396caeebf53cff7d355a552b2a4b2f))
* Add Excalidraw support to Writing component ([e7dfbd0](https://github.com/Innei/sprightly/commit/e7dfbd0b2de363b4b680c6e2760dced14c75d0eb))
* add global alias ([cf3c7eb](https://github.com/Innei/sprightly/commit/cf3c7ebb8a2bed0c10f006e5be362ff4b7395c12))
* add revalidate ([01a9aaa](https://github.com/Innei/sprightly/commit/01a9aaae562ec8e4df4375bd74b52669e39553a8))
* add say background ([#319](https://github.com/Innei/sprightly/issues/319)) ([b864b96](https://github.com/Innei/sprightly/commit/b864b96ed56e88cdacacbcabe986c80af8841c5f))
* add support for `NOTE_DELETE` event in webhook route ([e5e56ba](https://github.com/Innei/sprightly/commit/e5e56baf2d2d04ac8e8eb4754e8e69f2021d818f))
* apply friend form in mobile ([348560a](https://github.com/Innei/sprightly/commit/348560ad8800867b535a7a69c3c45023b16c6e30))
* artifact script ([9f7f119](https://github.com/Innei/sprightly/commit/9f7f11915a772a62e2a55f3fe6aae5fb48216c1a))
* border color ([9228b16](https://github.com/Innei/sprightly/commit/9228b16844b8d6fa5b7bda97a6d141fb9286ef68))
* category slug reactive closes [#294](https://github.com/Innei/sprightly/issues/294) ([c6e8d69](https://github.com/Innei/sprightly/commit/c6e8d69f7a5e45eb65dea12a4c4f55a69a97d5f9))
* ci ([4c4f4e9](https://github.com/Innei/sprightly/commit/4c4f4e9304eb7f54798e9d4ae4e28d10fecff60a))
* cleanup ([5499991](https://github.com/Innei/sprightly/commit/549999185880206dc824d55a5a88f46cc8cac0a8))
* clerk can't interactive when modal open ([7209233](https://github.com/Innei/sprightly/commit/720923330ce676f682eb4e958d85477670f38e55))
* code block events handler in milkdown ([9092b9c](https://github.com/Innei/sprightly/commit/9092b9c76879ddd08d65d9024b100aab41992d48))
* code block initial style ([d38f314](https://github.com/Innei/sprightly/commit/d38f314411c0f17f36b330cc3ee4554917c0d42c))
* code diff ([1435df2](https://github.com/Innei/sprightly/commit/1435df2672a48b51d7a1372ac4d8a67674995c6d))
* code editor ([ff4137c](https://github.com/Innei/sprightly/commit/ff4137c4c2c53b46b5e6f1d2ce75b8b4cff59139))
* code node clean ([9a2535d](https://github.com/Innei/sprightly/commit/9a2535d429ee8093a09e14539254a79d00f927e8))
* code scrollbar align ([2d9fa2f](https://github.com/Innei/sprightly/commit/2d9fa2fa314a1f6a3099ad24f3c62e1e52e37ac9))
* code style ([66528c4](https://github.com/Innei/sprightly/commit/66528c421094306b516900f373df144e5d59a167))
* comment cls ([4d38b0f](https://github.com/Innei/sprightly/commit/4d38b0f365bb39a71ad6cf27c13cb655051fd960))
* comment content title cell ([2fdf655](https://github.com/Innei/sprightly/commit/2fdf65553bbe786f5b8a1dc0a629c72f6c3938d1))
* comment modal style ([887e44b](https://github.com/Innei/sprightly/commit/887e44bcd4562115a67e415c0c034b7afee881d3))
* comment reply mutation ([bbd6f90](https://github.com/Innei/sprightly/commit/bbd6f9012043a9b3cbf08b5b8ab46858e424e900))
* comment source style ([38f395d](https://github.com/Innei/sprightly/commit/38f395d0c61d52524d94eb66618a3408b6c59bf0))
* copy button style ([861fe47](https://github.com/Innei/sprightly/commit/861fe474fdd61fd515c8afd4220e71d0f8a21ac3))
* copyright ([ae65b25](https://github.com/Innei/sprightly/commit/ae65b253c448cb29f7cf5ee87b039603dcf49513))
* data source error ([9e53302](https://github.com/Innei/sprightly/commit/9e53302374883b78beffc7e1b16d1b385a09259f))
* debug ([a890612](https://github.com/Innei/sprightly/commit/a89061213cc87cf96a95d6d95663fd2c4b8042f4))
* **deps:** update all non-major dependencies ([a1298f4](https://github.com/Innei/sprightly/commit/a1298f471107568e3807afc183607af2cea56104))
* **deps:** update all non-major dependencies ([29e98cd](https://github.com/Innei/sprightly/commit/29e98cddda1dfc208a42cb548b02394954c225c1))
* **deps:** update all non-major dependencies ([cce80a9](https://github.com/Innei/sprightly/commit/cce80a9b783e272a824bafd6d0cffaa838545ffe))
* **deps:** update all non-major dependencies ([613d684](https://github.com/Innei/sprightly/commit/613d6848bb9beaa1cb658035a2ff29e11f214e6f))
* **deps:** update all non-major dependencies ([a11f2f4](https://github.com/Innei/sprightly/commit/a11f2f4918bd1f7cd0ec9547cd624f4179bc8d2f))
* **deps:** update all non-major dependencies ([5861fa4](https://github.com/Innei/sprightly/commit/5861fa4f00ca3ab8d89aeec4c884ba5e8bd1079e))
* **deps:** update all non-major dependencies ([#218](https://github.com/Innei/sprightly/issues/218)) ([0aed3f7](https://github.com/Innei/sprightly/commit/0aed3f7eeb7fd35bd2a2788662911213a9a00f9c))
* **deps:** update all non-major dependencies ([#248](https://github.com/Innei/sprightly/issues/248)) ([9630d03](https://github.com/Innei/sprightly/commit/9630d035829687ad7949193ddb5514576779e599))
* **deps:** update all non-major dependencies ([#249](https://github.com/Innei/sprightly/issues/249)) ([a8bf331](https://github.com/Innei/sprightly/commit/a8bf33110727a8056fcb0c6c4006f6f5d72c10ca))
* **deps:** update all non-major dependencies ([#255](https://github.com/Innei/sprightly/issues/255)) ([f18d879](https://github.com/Innei/sprightly/commit/f18d8791df5d2c3eaf5c398da78d8020737870a1))
* **deps:** update all non-major dependencies ([#257](https://github.com/Innei/sprightly/issues/257)) ([187221a](https://github.com/Innei/sprightly/commit/187221a609aea95ef1c82f9e85252d2878eb8722))
* **deps:** update all non-major dependencies ([#260](https://github.com/Innei/sprightly/issues/260)) ([321fff7](https://github.com/Innei/sprightly/commit/321fff7dc57ca9cc7dc6bf1afbd067e140df1867))
* **deps:** update all non-major dependencies ([#272](https://github.com/Innei/sprightly/issues/272)) ([30c8287](https://github.com/Innei/sprightly/commit/30c82876c3c1b7411ab48083543c82450f87b42a))
* **deps:** update all non-major dependencies ([#277](https://github.com/Innei/sprightly/issues/277)) ([b7cbde6](https://github.com/Innei/sprightly/commit/b7cbde6dce499d42f3df4f557f8ca4a8c1cc8b04))
* **deps:** update all non-major dependencies ([#291](https://github.com/Innei/sprightly/issues/291)) ([46492a2](https://github.com/Innei/sprightly/commit/46492a20c1270171c392ffd619741c222b065911))
* **deps:** update all non-major dependencies ([#299](https://github.com/Innei/sprightly/issues/299)) ([a0474b3](https://github.com/Innei/sprightly/commit/a0474b36cd96f1a14584d3d620a1a21aca41b6da))
* **deps:** update all non-major dependencies ([#307](https://github.com/Innei/sprightly/issues/307)) ([29231d6](https://github.com/Innei/sprightly/commit/29231d68f90fb2d223c69e7126e0d011f7529db3))
* **deps:** update all non-major dependencies (minor) ([#268](https://github.com/Innei/sprightly/issues/268)) ([e0132ef](https://github.com/Innei/sprightly/commit/e0132efa7b6163b1a6313ac9b9ade5b4ff66234f))
* **deps:** update all non-major dependencies (minor) ([#283](https://github.com/Innei/sprightly/issues/283)) ([ab8d7a5](https://github.com/Innei/sprightly/commit/ab8d7a51d402d6b4dca2e1ec827d7cb5ebafcb57))
* **deps:** update all non-major dependencies (minor) ([#286](https://github.com/Innei/sprightly/issues/286)) ([72ba9f0](https://github.com/Innei/sprightly/commit/72ba9f0bfb852dc90e777dc9a5b920bc9fe19ab6))
* **deps:** update all non-major dependencies (patch) ([#215](https://github.com/Innei/sprightly/issues/215)) ([e391bc3](https://github.com/Innei/sprightly/commit/e391bc3b87a4ad107a6d45350e37b8e6e6fbeef6))
* **deps:** update all non-major dependencies to v5.15.0 ([#210](https://github.com/Innei/sprightly/issues/210)) ([cb3a75a](https://github.com/Innei/sprightly/commit/cb3a75a422d5a6cf4aac7b465d09fe04645654ee))
* **deps:** update dependency @aws-sdk/client-s3 to v3.509.0 ([#281](https://github.com/Innei/sprightly/issues/281)) ([80e9b25](https://github.com/Innei/sprightly/commit/80e9b2523c5c8d70a088558f4e7bd0ec8332b642))
* **deps:** update dependency @clerk/nextjs to v4.29.3 [security] ([#241](https://github.com/Innei/sprightly/issues/241)) ([0d67edc](https://github.com/Innei/sprightly/commit/0d67edce73eb796fc5a08b8c5da5a3f44ea345f4))
* **deps:** update dependency @mx-space/api-client to v1.7.2 ([#213](https://github.com/Innei/sprightly/issues/213)) ([eab47dd](https://github.com/Innei/sprightly/commit/eab47dd58786258b6bb9c40c4c6d5ad2282631f4))
* **deps:** update dependency axios to v1.6.3 ([#212](https://github.com/Innei/sprightly/issues/212)) ([a332f75](https://github.com/Innei/sprightly/commit/a332f759451524fc02e30c6fac8151212f77944f))
* **deps:** update dependency foxact to v0.2.28 ([#211](https://github.com/Innei/sprightly/issues/211)) ([7b0ee9c](https://github.com/Innei/sprightly/commit/7b0ee9ccb9c6d39d2f3c5ad6c1caf646ad5b48d8))
* **deps:** update dependency framer-motion to v11 ([#256](https://github.com/Innei/sprightly/issues/256)) ([7f83c15](https://github.com/Innei/sprightly/commit/7f83c15ca33128516657100eb71d096e7b811ca0))
* **deps:** update dependency marked to ^11.1.1 ([29db44c](https://github.com/Innei/sprightly/commit/29db44c04bf0cf35b7fd4767e2b943afc11edb65))
* **deps:** update dependency marked to v12 ([#274](https://github.com/Innei/sprightly/issues/274)) ([ef95da1](https://github.com/Innei/sprightly/commit/ef95da13063911bcb71297e8609426eae038b109))
* **deps:** update dependency postcss-import to v16 ([#224](https://github.com/Innei/sprightly/issues/224)) ([f426390](https://github.com/Innei/sprightly/commit/f426390c24a7cd36e9f79e4e876413011406310c))
* **deps:** update dependency react-toastify to v10 ([#250](https://github.com/Innei/sprightly/issues/250)) ([5b0b654](https://github.com/Innei/sprightly/commit/5b0b654761f95955d16c5941d14f8528ea852d3a))
* **deps:** update dependency vite to v5.0.12 [security] ([#253](https://github.com/Innei/sprightly/issues/253)) ([f31fab5](https://github.com/Innei/sprightly/commit/f31fab5348c6c31dff1a6d8588a410e737100313))
* disable auto focus comment box ([8b8ce23](https://github.com/Innei/sprightly/commit/8b8ce232fca930a830b180cabc90fa3174cad839))
* disable render code block in rss ([3f18021](https://github.com/Innei/sprightly/commit/3f18021cf0f34177d1883a1a3efe25b8317ee679))
* dom click event tracker ([c8f3337](https://github.com/Innei/sprightly/commit/c8f3337f34753a5cdfe860bc2bab86eeb7bf61c6))
* downgrade framer motion ([daa50a1](https://github.com/Innei/sprightly/commit/daa50a18f8bdd7ba525a3c57f123e4251794d528))
* drone config ([2504d2d](https://github.com/Innei/sprightly/commit/2504d2d783bbfb314088977c7cd3b26ff08db61d))
* duplicate Markdown heading id, closes  [#261](https://github.com/Innei/sprightly/issues/261) ([984acbe](https://github.com/Innei/sprightly/commit/984acbe7c90e0cd90c5f2bdc3554adcdc99d06d7))
* empty line of shiki line ([3db5128](https://github.com/Innei/sprightly/commit/3db512867a54c7dbfa65589fe1b69aa0c2d8353d))
* env ([b4c74e3](https://github.com/Innei/sprightly/commit/b4c74e3abaab1bf72eb56d64555f5e3d0fbd9ff8))
* excalidraw modal event ([3833c80](https://github.com/Innei/sprightly/commit/3833c80d35af095154d3325bbe5afbfcd72fef04))
* excalidraw preview ([f9c9781](https://github.com/Innei/sprightly/commit/f9c97814519a81276e2e0eda1b3d5860e9eb6e1c))
* excalidraw preview button logic in mobile ([06e8284](https://github.com/Innei/sprightly/commit/06e8284f7a7ed46d5916bdac5b45e79029944416))
* Excalidraw render in ssr ([5915e47](https://github.com/Innei/sprightly/commit/5915e4709347c3824ca900f45ff2ece2b62e46ff))
* excalidraw theme ([94d4766](https://github.com/Innei/sprightly/commit/94d47663b0d382d9b0ae3dfa2f6ee08c0a129134))
* exclude header active status ([bf8b3ab](https://github.com/Innei/sprightly/commit/bf8b3abc070061d09d0e1eae94227179ba66f675))
* favicon ([d212c69](https://github.com/Innei/sprightly/commit/d212c69d9ebfd8764f09cdb6e58aaca5a829dfb2))
* fetch category error ([e9935f0](https://github.com/Innei/sprightly/commit/e9935f0fafd855a90e494c627c294fdec767e153))
* filter real markdown headings ([3baf262](https://github.com/Innei/sprightly/commit/3baf26273c7ccadbaf458553712634bc3d250584))
* float popover position calculation ([73fa809](https://github.com/Innei/sprightly/commit/73fa8099d55b1c490e9d64c1fa280b76018d2d3c))
* footer divider ([1f6bfd0](https://github.com/Innei/sprightly/commit/1f6bfd06f6a3d10096214d8d3735eb4516a2e154))
* force update ([7012092](https://github.com/Innei/sprightly/commit/7012092db96f8cc3e53b46d7237e56c0788a848a))
* framer motion bug and improve modal dismissal ([6204682](https://github.com/Innei/sprightly/commit/6204682ce53028ded59fe14aabe442e899b139de))
* friend desc text balance ([33b43dc](https://github.com/Innei/sprightly/commit/33b43dcbb2ea6754303fa62618740794df7818a2))
* full movie card ([5934fc3](https://github.com/Innei/sprightly/commit/5934fc381d94256b559c89b0d997bed603a6495e))
* get pos and insert node ([bcb3e5b](https://github.com/Innei/sprightly/commit/bcb3e5b6fab5308d25f2ca06b8455a73d1ed30c2))
* github page not discover `_` prefix file ([8e2adfa](https://github.com/Innei/sprightly/commit/8e2adfaa2eceffca21ab63ec8691befe34366714))
* hack nextjs bug 60021 ([ef7a0d8](https://github.com/Innei/sprightly/commit/ef7a0d831c0886259510a5b1a41bad35857ff5a9))
* header ([b2d5e68](https://github.com/Innei/sprightly/commit/b2d5e6892c8d1b2f21711dad5818451466df3780))
* **header-sheet:** auto close when click ([c849680](https://github.com/Innei/sprightly/commit/c849680b07177045d8bccf204180a7e5aa7c2468))
* hide activity icon when reading focus ([b54e687](https://github.com/Innei/sprightly/commit/b54e6878508960bcb99533e7c2f07574aeba6c04))
* hide back to top fab in mobile ([4c20d96](https://github.com/Innei/sprightly/commit/4c20d960d021ac0215b4e09fc53a9d94100429a9))
* hide header cover in print ([d9d7315](https://github.com/Innei/sprightly/commit/d9d73151b203f2c6823605fb1f42d290d82373be))
* icons not aligned & modal window height not enough ([#217](https://github.com/Innei/sprightly/issues/217)) ([98e1084](https://github.com/Innei/sprightly/commit/98e1084fde9d8060de00ac0802ead62ad0639747))
* image grid ([c1ef2db](https://github.com/Innei/sprightly/commit/c1ef2db2bc174379c93e03c518a6c05d344b93e3))
* image src ([34f5b73](https://github.com/Innei/sprightly/commit/34f5b7353e5ecc969c11fce9febd976de30cd3dd))
* images grids ([1f43e6c](https://github.com/Innei/sprightly/commit/1f43e6cf8490213b41d60b0dfe89a22408e75eec))
* import ([d3d7612](https://github.com/Innei/sprightly/commit/d3d76129efb0bbb55a34f36a09244d0ca28edb71))
* improve favicon for md fixes [#298](https://github.com/Innei/sprightly/issues/298) ([a84cb55](https://github.com/Innei/sprightly/commit/a84cb554f4ba7f58ec6b0de1d5b31849f044f94a))
* improve ux ([139cf2e](https://github.com/Innei/sprightly/commit/139cf2e960d7f06816d707b11c2b12b1ca22886f))
* jotai store and ui bg ([82690a1](https://github.com/Innei/sprightly/commit/82690a116f300c776addf3e654859a2bdc340683))
* katex inline, closes [#237](https://github.com/Innei/sprightly/issues/237) ([fd59a7a](https://github.com/Innei/sprightly/commit/fd59a7a31a015f3d786bd1f9cdad40992d826d07))
* keyboard shortcut for opening search panel ([61587c0](https://github.com/Innei/sprightly/commit/61587c0328d3406e140570f79caee9058d7446e5))
* **link-card:** add fallback url ([f58c9d2](https://github.com/Innei/sprightly/commit/f58c9d228aa5b5b8f53708f9d3a6564490624195))
* lockfile ([30a37be](https://github.com/Innei/sprightly/commit/30a37be705d504571c9b73ad17fccf1e5d859509))
* lockfile ([235837f](https://github.com/Innei/sprightly/commit/235837f92e7bf47a77f26d26eb4a64a362a72c35))
* mark text color ([83d85ab](https://github.com/Innei/sprightly/commit/83d85aba0773d5e13927dc43991ea70d6cf4e357))
* markdown banner style ([9d207d5](https://github.com/Innei/sprightly/commit/9d207d5d64ada3581bc4b09b99ebff5c761dfc13))
* markdown mark style ([97ab708](https://github.com/Innei/sprightly/commit/97ab7081eaa945b96bac2ae6970d3e85eca844c0))
* markdown video align center ([ea25d83](https://github.com/Innei/sprightly/commit/ea25d838168f9a79f2cfd1e2740fa82ecad109e6))
* max-height of header sheet ([aa6b4fc](https://github.com/Innei/sprightly/commit/aa6b4fc0e4d9b2a614d5678b7d20c74f4087ee93))
* motion ([dcba4ec](https://github.com/Innei/sprightly/commit/dcba4eca3c24328cb5edd735a2792afa5daa4759))
* movie card style ([dbb4791](https://github.com/Innei/sprightly/commit/dbb47919896a63c344ce7011d5db2d8edae1b442))
* my domain copyright ([fa650b9](https://github.com/Innei/sprightly/commit/fa650b99f78c4d07fef255f65ccc82df1b04778a))
* next image ([4274a17](https://github.com/Innei/sprightly/commit/4274a17d95ee03a915c965eff92433f94e8680b7))
* note blockquite style ([a0da237](https://github.com/Innei/sprightly/commit/a0da2370d6d17dae806cdbb060acc602372d1ce2))
* npmrc ([7da78a5](https://github.com/Innei/sprightly/commit/7da78a5df02f5ee7d3b6e192685928d11c1ce206))
* og padding ([74fd89c](https://github.com/Innei/sprightly/commit/74fd89c4161e756cf70c1c0c0562cd6a08144590))
* og padding ([dcd2d33](https://github.com/Innei/sprightly/commit/dcd2d33f8f690956aae11f4693d934d0d7b108de))
* og title ([95f54c2](https://github.com/Innei/sprightly/commit/95f54c2ac9111f926727a2f601401687bd3341fb))
* og title overflow ([75ba051](https://github.com/Innei/sprightly/commit/75ba0516d5ed7520433981073321ced8561d7fd9))
* oklch color channel ([75432f8](https://github.com/Innei/sprightly/commit/75432f8bd62c5d8671bbbb8c950a73720f989e09))
* optimize ui ([a58f629](https://github.com/Innei/sprightly/commit/a58f629db567d18e4e7ae683ee60870198560b25))
* overflow ([70c6079](https://github.com/Innei/sprightly/commit/70c6079315a7018fe23ae7bfe02bed2d10163c9f))
* peek link ([d2627f7](https://github.com/Innei/sprightly/commit/d2627f7de5f2d15f3b3d1572381ef1b7b9c8ea52))
* post related ([d8ba333](https://github.com/Innei/sprightly/commit/d8ba333fe92f2320e21c4bb61951a01c323c23e0))
* presence calculation ([6fe7f65](https://github.com/Innei/sprightly/commit/6fe7f658ed095fe671b04bcdb963f9d93c94ba48))
* process icon ([212aa05](https://github.com/Innei/sprightly/commit/212aa05c2bdefbf7bfac91fec44ea0eb8d819235))
* process name ([cd6044d](https://github.com/Innei/sprightly/commit/cd6044d1f43248f8bd81edf71cfc0d2749ad77ce))
* react render component style ([28bc85f](https://github.com/Innei/sprightly/commit/28bc85f5f535bd834696eef15e7219c42af39825))
* refresh can apply friends ([7c6ca38](https://github.com/Innei/sprightly/commit/7c6ca3860d0e2d1e0744895cc4d2dc48ad8c0603))
* refresh token ([0ab7f0d](https://github.com/Innei/sprightly/commit/0ab7f0d91f6cbb79e7872c83c1a04776e1ec854f))
* remote component render ([2401325](https://github.com/Innei/sprightly/commit/240132543c8f46717fd3dc75c3a7773ddf6f45d6))
* remove `A` ([6961dc0](https://github.com/Innei/sprightly/commit/6961dc086bcd915cb0f8f2fedeed368491750774))
* remove `revalidateTag` ([93a4f23](https://github.com/Innei/sprightly/commit/93a4f239a9e3969e27616a80396d54548640e385))
* remove cache ([00a7551](https://github.com/Innei/sprightly/commit/00a7551cfab68127491fc625a19934d01779958a))
* remove fuck unstable_cache ([c5c7f23](https://github.com/Innei/sprightly/commit/c5c7f232e449ab142b01a4342ad4620b81b5b917))
* remove ring style ([5aa9c85](https://github.com/Innei/sprightly/commit/5aa9c85640b1f138e82c73e2b1dc42b49ef66ce6))
* remove route state replace ([acc815e](https://github.com/Innei/sprightly/commit/acc815e6bd94b278902135ee4a48478a355c9a94))
* remove stupid code ([e5d3a08](https://github.com/Innei/sprightly/commit/e5d3a08e9e337947517925b83c06f1178e94455e))
* remove 数据万象 params ([2a393b7](https://github.com/Innei/sprightly/commit/2a393b7280312edd6af5ba68fd493eb5f65d5095))
* render alert rule but can not edit now ([9efdf29](https://github.com/Innei/sprightly/commit/9efdf2958afe466746378fd9bcf529edc4d701f5))
* reply comment ([946b234](https://github.com/Innei/sprightly/commit/946b2345e9b580d94aba887c2eebb432e2090a27))
* requestErrorHandler to fetchQuery calls ([8844c36](https://github.com/Innei/sprightly/commit/8844c36949bca765400e49cdc72637372cc9a85f))
* reset auto save data when saved ([c5bae13](https://github.com/Innei/sprightly/commit/c5bae1396b8fd54585c64a917e871b7acb018e1a))
* reset initial data ([12351b9](https://github.com/Innei/sprightly/commit/12351b900790fbf036699114d3cbc377600127a0))
* room context ([8ec33d1](https://github.com/Innei/sprightly/commit/8ec33d1003aac2272c3d6e6ba3ed1f8156ede52b))
* route enum ([f5a9ff8](https://github.com/Innei/sprightly/commit/f5a9ff8b58576da73d49f380e3bda93d1f9df292))
* rss route export ([d018f74](https://github.com/Innei/sprightly/commit/d018f74a27ecc5c810287e84539916a1b4cd26cb))
* scroll area ([8085e65](https://github.com/Innei/sprightly/commit/8085e65459875f7bf4e4e8d888c72b65c68921c7))
* scrollbar color ([1eaad9b](https://github.com/Innei/sprightly/commit/1eaad9b1508c4a6218038bff237c48fe102f0990))
* search panel loading spinner bug ([33fa4ba](https://github.com/Innei/sprightly/commit/33fa4bae1facb8cb4de9ce9be13e48fdcb044aa0))
* seesion key case ([f20edb9](https://github.com/Innei/sprightly/commit/f20edb9656bab353455181b694f310b102dc7f0d))
* select loading style ([19fb3d2](https://github.com/Innei/sprightly/commit/19fb3d204a897772350fd779845907aee5df1aaa))
* seo favicon ([3b4f23c](https://github.com/Innei/sprightly/commit/3b4f23cd30380bb2ffb0b9d6f405b90a9c283758))
* set `w-full` first ([9b38e09](https://github.com/Innei/sprightly/commit/9b38e09671a7df23c680d20eea1e0274b06d9ea9))
* set image view max-w ([32f076c](https://github.com/Innei/sprightly/commit/32f076cc1a6b1223a1457c3a5a6db3ecbec025e3))
* shiki copy button position ([65a64fb](https://github.com/Innei/sprightly/commit/65a64fb1ff71c8dacfec1adf346ede7cfe9546be))
* shiki highlight line ([a3769cb](https://github.com/Innei/sprightly/commit/a3769cbb368125d3f62c45d56311666da078d352))
* social icon overflow ([ff12ca8](https://github.com/Innei/sprightly/commit/ff12ca8a0c5380e50f3b5645ca6d41b9b9b5549a))
* some styles ([9b53db0](https://github.com/Innei/sprightly/commit/9b53db0960e440a9e901f9c64a52f9935481effc))
* source ([afcfd4b](https://github.com/Innei/sprightly/commit/afcfd4b0da875a9f7567e2c70a14988f91c23b1c))
* **storybook:** update deps ([111fc65](https://github.com/Innei/sprightly/commit/111fc6580726111dc83c730cb364210fbe3992c7))
* style ([1dd58b6](https://github.com/Innei/sprightly/commit/1dd58b657629971bd95cb1e4fed07552564852e8))
* summary condition ([c009425](https://github.com/Innei/sprightly/commit/c009425b7898c8df311d26c29da92c90ad6fdd83))
* summary render ([b465230](https://github.com/Innei/sprightly/commit/b4652309acc6e420d25aa695cbf86c07182fb8e3))
* svg preview viewport ([7888b72](https://github.com/Innei/sprightly/commit/7888b72c95aa2e9f776a57de8a3e8549fcad7343))
* tab content cls ([cb40ff6](https://github.com/Innei/sprightly/commit/cb40ff62283ebd29a3668f783479fab783fcb17a))
* tag value ([10aa47f](https://github.com/Innei/sprightly/commit/10aa47f5e8cc85651d7bda0fc9c811cc490f3159))
* theme provider ([cc58472](https://github.com/Innei/sprightly/commit/cc584722564aa3b60d274db708210e600a727cca))
* to admin ([88f4523](https://github.com/Innei/sprightly/commit/88f45232b3c987dc505d7e8a32a917c433e1c37f))
* transition view ([20b2d11](https://github.com/Innei/sprightly/commit/20b2d114dfdf23d3cfc1796ec5346cc25744420c))
* transition view ([fede3f6](https://github.com/Innei/sprightly/commit/fede3f65bf9f658058658f9e6578d0fdd6b81de3))
* type ([84ed850](https://github.com/Innei/sprightly/commit/84ed850ca87dfa3b7414832db7276781b16bd99a))
* type error ([d2d2806](https://github.com/Innei/sprightly/commit/d2d2806f2715a194dceac4672db39613899c9bb2))
* type error ([30a212b](https://github.com/Innei/sprightly/commit/30a212b474891eb925d6531f5df691669f82f0a8))
* typing error ([a3ea932](https://github.com/Innei/sprightly/commit/a3ea932d0a43c49ea22d1dbbe72d97c0753a8ffc))
* unoptimize some image ([8f88dc9](https://github.com/Innei/sprightly/commit/8f88dc9b5b23f9b16241265985bf3f2cf882881e))
* update copy button ([a94194e](https://github.com/Innei/sprightly/commit/a94194e1cd1e2d99b8972455bd7af51a5de6669a))
* update data when save csb successful ([31529c0](https://github.com/Innei/sprightly/commit/31529c085bc3077ee44bfb9562d1e1de435f4e86))
* update Excalidraw component styling ([9671837](https://github.com/Innei/sprightly/commit/9671837d8bae9fed6c933f0d167c9a9b8e6e05c4))
* update katex to v0.16.9 and prevent throwing ParseError ([#227](https://github.com/Innei/sprightly/issues/227)) ([8873514](https://github.com/Innei/sprightly/commit/8873514226cea2dd1e8c3f2bdb9c35f331f3f2bb))
* Update NoteTopicInfo.tsx ([2c98d46](https://github.com/Innei/sprightly/commit/2c98d464e38adca56490093dc3377af1fd9cf378))
* update style ([453d7c9](https://github.com/Innei/sprightly/commit/453d7c9aeb6b0d5a82364fbd16a1f5bce43e9496))
* update tags modal style ([612efd9](https://github.com/Innei/sprightly/commit/612efd90d1b9350f8e58626185f63906ba9b7f8b))
* update thing ([6cbf46a](https://github.com/Innei/sprightly/commit/6cbf46af59b1345b4ccbc9758c35ea49e36e4c67))
* upload need token ([f0453db](https://github.com/Innei/sprightly/commit/f0453dbe2333d073f106c345dba11449205c9d26))
* use client component of note home page ([fde1bca](https://github.com/Innei/sprightly/commit/fde1bca03dba0ed42f346997a5c1f091d731ae20))
* use put instead of patch in post update ([1620699](https://github.com/Innei/sprightly/commit/162069903b366ed2a92ff2209b18dba5ddd62623))
* vercel cache ([acdd7f6](https://github.com/Innei/sprightly/commit/acdd7f6dadde2c4e8056a8d5c9336c493cea0fa7))
* vertical container layout style ([f5d84ca](https://github.com/Innei/sprightly/commit/f5d84cab10c18e4de7550988625ab6b741165acb))
* webhook type ([e676c3f](https://github.com/Innei/sprightly/commit/e676c3f696735d453c750e8adfe2e29b3849eda9))


### Features

*  add feed metadata ([1c9aba2](https://github.com/Innei/sprightly/commit/1c9aba2a332ed5a8e305a5ce1a3deedb2b0803e7))
* add @mx-space/webhook dependency and update revalidate time ([99dca50](https://github.com/Innei/sprightly/commit/99dca509b7de05b2d73fbc883ed139bb106caeea))
* add activity rooms ([4426fa6](https://github.com/Innei/sprightly/commit/4426fa680c2d1b8a276dfaccfc0f53f4e55633e6))
* add comment source ([28e71b4](https://github.com/Innei/sprightly/commit/28e71b4d4ba3f3e4398ccc5f4890c964d05a085f))
* Add language support & fix: Tooltip now hides when scrolling to… ([#305](https://github.com/Innei/sprightly/issues/305)) ([0761eaa](https://github.com/Innei/sprightly/commit/0761eaad1e5f15e8283c4043f4339093cba2c036))
* add s3 uploader ([244e96e](https://github.com/Innei/sprightly/commit/244e96ea47123e7c46b08d4caf07beff1cce9c3a))
* add some block in milkdown editor ([#270](https://github.com/Innei/sprightly/issues/270)) ([6b1635b](https://github.com/Innei/sprightly/commit/6b1635b96bfec39e537f07724990f3843ea08ebf))
* add star count ([04ad6d0](https://github.com/Innei/sprightly/commit/04ad6d0d1d02414099c90ca3664893d05da0f9ce))
* Add support for Mermaid diagrams in Writing component ([488ca57](https://github.com/Innei/sprightly/commit/488ca57d9c4bbaf6e5e0b8177d67fccc787450c1))
* Add support for unknown languages & style: Update code line for… ([#306](https://github.com/Innei/sprightly/issues/306)) ([59a8f61](https://github.com/Innei/sprightly/commit/59a8f61d085079511ac52505947468f4b341b1dc))
* autosave ([6daaf06](https://github.com/Innei/sprightly/commit/6daaf06530ac0cdc5cb3ece43f293493e39911f2))
* cache more ([0fe5b44](https://github.com/Innei/sprightly/commit/0fe5b44654ff42461ff4a4539480d3eb9a86667f))
* cache something ([6848154](https://github.com/Innei/sprightly/commit/6848154c19c54d6dcbc7a9e3250358ca465489ea))
* comment markdown improve ([65d4618](https://github.com/Innei/sprightly/commit/65d4618fe4aa14bdc0e59febfaa3a66c9566dc90))
* copy excalidraw data ([6897840](https://github.com/Innei/sprightly/commit/68978407a802b3dfe7fbd52f35fa532acb6c305d))
* discord icon, fixes [#315](https://github.com/Innei/sprightly/issues/315) ([f91267e](https://github.com/Innei/sprightly/commit/f91267e51e5871c622899b0b91d97288f5df83e4))
* editor add menubar ([#231](https://github.com/Innei/sprightly/issues/231)) ([8feb41f](https://github.com/Innei/sprightly/commit/8feb41fe742d323c24b037086b29003c007f1c79))
* feature provider ([1617ca8](https://github.com/Innei/sprightly/commit/1617ca8adadc540c5e42b66d09f2b3b57e24526a))
* form transform value ([661fe5c](https://github.com/Innei/sprightly/commit/661fe5c2b377a719243d11985bf461fe6916de51))
* hack route blocker ([#236](https://github.com/Innei/sprightly/issues/236)) ([0826096](https://github.com/Innei/sprightly/commit/0826096de8a34233a63e82fc2b58bf1288451dd0))
* home og ([015ca83](https://github.com/Innei/sprightly/commit/015ca839d4d6e299e7e12eb6a83684f430dd78c2))
* image node view ([c7c1fc0](https://github.com/Innei/sprightly/commit/c7c1fc0e4826af26b5698adad20a041ca69d4a96))
* improve donate modal in mobile ([148bc2e](https://github.com/Innei/sprightly/commit/148bc2ebe289da3338078e92c897341e4f27d8bb))
* improve mobile paper cover style ([2092aa2](https://github.com/Innei/sprightly/commit/2092aa27ed0453cc4e991774605372614f61b2ad))
* json highlighter editor ([06db5eb](https://github.com/Innei/sprightly/commit/06db5ebcf5767f775466e0bdf07b0138cc9d2611))
* lazyload image ([59d1944](https://github.com/Innei/sprightly/commit/59d1944fb70c79e43869b46c7a639973a9d42c76))
* light dashboard ([#228](https://github.com/Innei/sprightly/issues/228)) ([dc5ed08](https://github.com/Innei/sprightly/commit/dc5ed0847bc87cbc4f67aba6fa8ebeeefa1fbfd7))
* **markdown:** add grid type ([868dbcb](https://github.com/Innei/sprightly/commit/868dbcb1b42e5b0a336b18ad79c08602542a056b))
* **markdown:** images grid ([5e08d59](https://github.com/Innei/sprightly/commit/5e08d59fea599c4575026dfd8d97ca5935ce9e8f))
* **md:** support load remote react component ([9e28418](https://github.com/Innei/sprightly/commit/9e284181a9e2cba89d40cda3a0da5c295660d178))
* note topic rank ([8918ba7](https://github.com/Innei/sprightly/commit/8918ba7ef6016aa31934840d72c7889ded22a7b4))
* re-add loading file ([9112c1b](https://github.com/Innei/sprightly/commit/9112c1b28d1bc18bb85f86525c0b4721a03f8e97))
* runtime env ([#320](https://github.com/Innei/sprightly/issues/320)) ([55b994c](https://github.com/Innei/sprightly/commit/55b994cffe43d32e35c729f477a96a19a5d46905))
* shiki ([#303](https://github.com/Innei/sprightly/issues/303)) ([207658f](https://github.com/Innei/sprightly/commit/207658f6bb17330c49e36926fca9570291309d83))
* shiki add line number, fixes [#309](https://github.com/Innei/sprightly/issues/309) ([e76df6f](https://github.com/Innei/sprightly/commit/e76df6fc8a98aa27fdc9b645fdb702c67e469c47))
* support code block for excalidraw ([5fd5b64](https://github.com/Innei/sprightly/commit/5fd5b646ab4dcb9ec5229338e2b752e2f91afd41))
* support delta of excalidraw data structure ([bec612d](https://github.com/Innei/sprightly/commit/bec612d740c7b82e59de450aa078b57ba048f5fd))
* support manual summary ([2eb880a](https://github.com/Innei/sprightly/commit/2eb880a6e98848a98bcc1c10ea4d893ef15956f6))
* support realtime page reader presence ([#288](https://github.com/Innei/sprightly/issues/288)) ([abe2460](https://github.com/Innei/sprightly/commit/abe2460b191d2db503aa10fe90fd6dc47e095833))
* support tmdb link card parser ([a70f638](https://github.com/Innei/sprightly/commit/a70f63840c9fb8e1362987977f32251a5339822c))
* update generated noise background ([cfb8c6a](https://github.com/Innei/sprightly/commit/cfb8c6a9d6777274fe3aafc9bc8d5318875562e2))
* upgrade daisyui ([76c0596](https://github.com/Innei/sprightly/commit/76c059687acda18eb3731f8c1f36c607c1f30de9))
* ws event real read count ([0a61a81](https://github.com/Innei/sprightly/commit/0a61a81e36e359de3e08e5e1e3c0a50228404d1c))
* 添加一些windows上的进程名称 ([#276](https://github.com/Innei/sprightly/issues/276)) ([8d39b48](https://github.com/Innei/sprightly/commit/8d39b4800b990c002f72ddca922a14b92ece7c16))



# [1.0.0](https://github.com/Innei/sprightly/compare/v0.2.0...v1.0.0) (2023-12-26)


### Bug Fixes

* accent color style override ([8e4808d](https://github.com/Innei/sprightly/commit/8e4808d6b5e4d81021da5c8e9f8fff505fbe9e28))
* activity initial state ([737d7ed](https://github.com/Innei/sprightly/commit/737d7ed7346537170f0721132eaff346f97fa62c))
* **Activity:** correct Chinese app name of `safari`, add pm2 config ([#75](https://github.com/Innei/sprightly/issues/75)) ([09b0216](https://github.com/Innei/sprightly/commit/09b02164f854d479f45f7b95b282a0960ca918fd))
* add app webUrl for self link parser ([9eb7041](https://github.com/Innei/sprightly/commit/9eb7041b7cd2e70f25a08751956e8cfbbdd21e4e))
* add cover for preview modal ([4000079](https://github.com/Innei/sprightly/commit/40000792303a557bcf3740374fe847b28d2f2c42))
* add loading page for post detail ([457d6c6](https://github.com/Innei/sprightly/commit/457d6c601fe7d615c7b8ca568b03f70540a61f06))
* add margin for post copyright section ([0866177](https://github.com/Innei/sprightly/commit/0866177a1d4fc7f40d103d07ce563e35f4f4fed7))
* add margin to NoteBottomBarAction component ([950ea4b](https://github.com/Innei/sprightly/commit/950ea4b903f200378b8d61838ec444125c5f055d))
* add webfont ([7f2dc23](https://github.com/Innei/sprightly/commit/7f2dc23dc94b2465e225218c9478471c0fab4a43))
* after clerk login when refresh token ([e5d76f6](https://github.com/Innei/sprightly/commit/e5d76f65f98f4c2c0e43e3f3180a2c2e83ae2d91))
* apply link style ([25d32ca](https://github.com/Innei/sprightly/commit/25d32ca1b4b48fd0aed0d7d025fd16cdac612823))
* attach client real ip ([da0ff23](https://github.com/Innei/sprightly/commit/da0ff2372cbddbd8a8a2ae0c7d18d0291a48d688))
* avatar & activity icon load failed ([#105](https://github.com/Innei/sprightly/issues/105)) ([11ccb0d](https://github.com/Innei/sprightly/commit/11ccb0d2db55d5ff9cc24a5103b507ed363de9ae)), closes [#104](https://github.com/Innei/sprightly/issues/104)
* banner svg icon and text gap, fix [#94](https://github.com/Innei/sprightly/issues/94) ([f7233cb](https://github.com/Innei/sprightly/commit/f7233cb2900f67952cb7560f6c5fb0e2220d0089))
* block link fallback render ([ae062ad](https://github.com/Innei/sprightly/commit/ae062adb6c78a833dc80f45c12cf9f1d706aa1c1))
* build ([7fe5ca1](https://github.com/Innei/sprightly/commit/7fe5ca19e38267379d42d59eafde5e86654e139f))
* center the gallery image ([7c63699](https://github.com/Innei/sprightly/commit/7c63699598ef80edb6d12c05c4b8acd0a0008e79))
* checkbox position ([f98c7ff](https://github.com/Innei/sprightly/commit/f98c7ff58670a61c2d6002dbbc38b9011949c6f7))
* ci cmd ([4ec3b81](https://github.com/Innei/sprightly/commit/4ec3b8155060a0c7682471b0cd43795525600d69))
* clerk card padding top closes [#86](https://github.com/Innei/sprightly/issues/86) ([ed3f4f9](https://github.com/Innei/sprightly/commit/ed3f4f9264dc5598e335b5afb1d2a47e0aa931ec))
* codeblock `COPY` position ([9448a46](https://github.com/Innei/sprightly/commit/9448a46bb20da5cf2c7c9699d036ff5540eb25f5))
* comment avatar placeholder ([385a07a](https://github.com/Innei/sprightly/commit/385a07a04036e489eb7f45801a0778bf5cd72d81))
* delete think button text ([1fb9944](https://github.com/Innei/sprightly/commit/1fb9944ef434a29402e9509cd564a1799793485d))
* demo type ([8b60a24](https://github.com/Innei/sprightly/commit/8b60a245c2ef5e1acc568c24bc217eb184558d3e))
* **deps:** update all non-major dependencies ([56e76d6](https://github.com/Innei/sprightly/commit/56e76d6a2128f8a1e472678c638a100374afa9ec))
* **deps:** update all non-major dependencies ([b14b3de](https://github.com/Innei/sprightly/commit/b14b3ded4fca03905a8095a956e7ade47943d077))
* **deps:** update all non-major dependencies ([c2612ce](https://github.com/Innei/sprightly/commit/c2612ce1cdd61a2e1b5bfb7a3e847f7b3977cfb1))
* **deps:** update all non-major dependencies ([562774a](https://github.com/Innei/sprightly/commit/562774acf3bbe4d83a413768e8180251c755daad))
* **deps:** update all non-major dependencies ([50ac3bb](https://github.com/Innei/sprightly/commit/50ac3bb77efdfb7d3a55b64cd84246633c881eef))
* **deps:** update all non-major dependencies ([ae5f27a](https://github.com/Innei/sprightly/commit/ae5f27a6435a82df551293bf786ddce5d0b52ed5))
* **deps:** update all non-major dependencies ([58c51e9](https://github.com/Innei/sprightly/commit/58c51e92e67e4f78e938480e4e1d4a6a6b9b1a98))
* **deps:** update all non-major dependencies ([f1b3d92](https://github.com/Innei/sprightly/commit/f1b3d9220aaf5464ffcd43c3e6eccba60a675da5))
* **deps:** update all non-major dependencies ([0330558](https://github.com/Innei/sprightly/commit/0330558f8df6e037828464c3f63bc3abec0059fd))
* **deps:** update all non-major dependencies ([84a3830](https://github.com/Innei/sprightly/commit/84a383050f984603939aa467e6e15f603015ed9a))
* **deps:** update all non-major dependencies ([8df6fae](https://github.com/Innei/sprightly/commit/8df6faeb221e6c7330106680db3ba87ec343d2c5))
* **deps:** update all non-major dependencies ([db5f9f5](https://github.com/Innei/sprightly/commit/db5f9f54b81d985055c874079ca384d532528095))
* **deps:** update all non-major dependencies ([8737491](https://github.com/Innei/sprightly/commit/873749197f65959575a737badcb6aaf2a199ef30))
* **deps:** update all non-major dependencies ([c084602](https://github.com/Innei/sprightly/commit/c084602d9b58989ff71f51eaa4b712384a0ab2e8))
* **deps:** update all non-major dependencies ([5405e8f](https://github.com/Innei/sprightly/commit/5405e8fa44c2bc4b07e8d93e877bd127f4d95b4f))
* **deps:** update all non-major dependencies ([0362a73](https://github.com/Innei/sprightly/commit/0362a7399ce4651efa55e01459a35eae39eb9bc5))
* **deps:** update all non-major dependencies ([1acf473](https://github.com/Innei/sprightly/commit/1acf4733d9fa9aa7413fea30bc653d50a3c644e8))
* **deps:** update all non-major dependencies ([212eaea](https://github.com/Innei/sprightly/commit/212eaea37683ddc949c4b18a9c582d696aa86667))
* **deps:** update all non-major dependencies ([0ef398f](https://github.com/Innei/sprightly/commit/0ef398f8706778d8c09b6559ef224bece92c0098))
* **deps:** update all non-major dependencies ([6d89e11](https://github.com/Innei/sprightly/commit/6d89e112791218bbfa3f9a70f7b8ac7cd7781520))
* **deps:** update all non-major dependencies ([7fd50f6](https://github.com/Innei/sprightly/commit/7fd50f656cf07d0ccbdc729b1e92c50742ebda93))
* **deps:** update all non-major dependencies ([455f501](https://github.com/Innei/sprightly/commit/455f50119502a138bc4180fe4afac5055c85ad29))
* **deps:** update all non-major dependencies ([#101](https://github.com/Innei/sprightly/issues/101)) ([8e2efef](https://github.com/Innei/sprightly/commit/8e2efefc1e2a7be1d022659bd994c4c19e096dfd))
* **deps:** update all non-major dependencies ([#102](https://github.com/Innei/sprightly/issues/102)) ([c6c3437](https://github.com/Innei/sprightly/commit/c6c34374c33aaa27b83b3b4f13d765f95b8f86ed))
* **deps:** update all non-major dependencies ([#115](https://github.com/Innei/sprightly/issues/115)) ([ada8bcb](https://github.com/Innei/sprightly/commit/ada8bcb033cae93e1d339c0b89aa858621b2590f))
* **deps:** update all non-major dependencies ([#140](https://github.com/Innei/sprightly/issues/140)) ([a839d58](https://github.com/Innei/sprightly/commit/a839d58c9a51e66adcb436d17d4a13d2308e8376))
* **deps:** update all non-major dependencies ([#143](https://github.com/Innei/sprightly/issues/143)) ([029876f](https://github.com/Innei/sprightly/commit/029876f0d0e8f410276e0a0adcb562e5190d2f84))
* **deps:** update all non-major dependencies ([#145](https://github.com/Innei/sprightly/issues/145)) ([06adcf6](https://github.com/Innei/sprightly/commit/06adcf6a133f9bbf8e76b292eb1c8b464bfa8850))
* **deps:** update all non-major dependencies ([#171](https://github.com/Innei/sprightly/issues/171)) ([6e09012](https://github.com/Innei/sprightly/commit/6e09012d8baf8e3eaadf2c835ecc02025ba2682e))
* **deps:** update all non-major dependencies ([#178](https://github.com/Innei/sprightly/issues/178)) ([3f8097e](https://github.com/Innei/sprightly/commit/3f8097e0dc0bbc3d21cee6e89d09ce6c415f7b2d))
* **deps:** update all non-major dependencies ([#183](https://github.com/Innei/sprightly/issues/183)) ([83175e8](https://github.com/Innei/sprightly/commit/83175e882aa5bc43289866da065da79a4845b476))
* **deps:** update all non-major dependencies ([#188](https://github.com/Innei/sprightly/issues/188)) ([13b5a52](https://github.com/Innei/sprightly/commit/13b5a52c0f5b0efa6554837c89f420847b9d8842))
* **deps:** update all non-major dependencies ([#199](https://github.com/Innei/sprightly/issues/199)) ([7b07026](https://github.com/Innei/sprightly/commit/7b070263cb93f543a95848dabdf6bd1d3b4d176b))
* **deps:** update all non-major dependencies ([#70](https://github.com/Innei/sprightly/issues/70)) ([3932f96](https://github.com/Innei/sprightly/commit/3932f96f0dfe57309170a6f18662f3529830e0b3))
* **deps:** update all non-major dependencies ([#92](https://github.com/Innei/sprightly/issues/92)) ([6fe4c96](https://github.com/Innei/sprightly/commit/6fe4c9672c199cdcbd416cf5ab38adc4063e8031))
* **deps:** update all non-major dependencies (minor) ([#148](https://github.com/Innei/sprightly/issues/148)) ([69b8cca](https://github.com/Innei/sprightly/commit/69b8ccac47450d5a53531016c861115658a88121))
* **deps:** update all non-major dependencies (minor) ([#160](https://github.com/Innei/sprightly/issues/160)) ([58867dd](https://github.com/Innei/sprightly/commit/58867dd4b976538bdb9f16f7166677c7bef0ccb2))
* **deps:** update all non-major dependencies (minor) ([#170](https://github.com/Innei/sprightly/issues/170)) ([5089604](https://github.com/Innei/sprightly/commit/5089604a4d4d5f126d74171e6da3b92d492163da))
* **deps:** update all non-major dependencies (minor) ([#89](https://github.com/Innei/sprightly/issues/89)) ([10a47b7](https://github.com/Innei/sprightly/commit/10a47b7b0674cbb478195c00985873f1dd8a8a11))
* **deps:** update all non-major dependencies (patch) ([#159](https://github.com/Innei/sprightly/issues/159)) ([1637ad4](https://github.com/Innei/sprightly/commit/1637ad49f71c103b5f431f990881eac8d891c9b8))
* **deps:** update all non-major dependencies to v5.10.0 ([#168](https://github.com/Innei/sprightly/issues/168)) ([cb33000](https://github.com/Innei/sprightly/commit/cb330003ac1c414b2940ccb0e7b4556ca19892c7))
* **deps:** update all non-major dependencies to v5.13.4 (minor) ([#181](https://github.com/Innei/sprightly/issues/181)) ([ba015f6](https://github.com/Innei/sprightly/commit/ba015f62fd8c95214ab284a0455ca5105c0b7ab3))
* **deps:** update all non-major dependencies to v5.14.6 ([#203](https://github.com/Innei/sprightly/issues/203)) ([e614b39](https://github.com/Innei/sprightly/commit/e614b39318553feebadb0301687a06cc245e7e62))
* **deps:** update all non-major dependencies to v5.4.3 (minor) ([#124](https://github.com/Innei/sprightly/issues/124)) ([0ef79c7](https://github.com/Innei/sprightly/commit/0ef79c7e6410b43e256b73d3f049b7f460694c5c))
* **deps:** update dependency @clerk/nextjs to v4.24.0 ([#85](https://github.com/Innei/sprightly/issues/85)) ([a5b0f12](https://github.com/Innei/sprightly/commit/a5b0f12291204ad5ce522d2783147fe1581c9528))
* **deps:** update dependency @radix-ui/react-select to v2 ([#93](https://github.com/Innei/sprightly/issues/93)) ([15dc545](https://github.com/Innei/sprightly/commit/15dc5458631e3cd227d77df520dc7a56dd0194d9))
* **deps:** update dependency @tanstack/react-query-devtools to v5.13.3 ([#177](https://github.com/Innei/sprightly/issues/177)) ([58b7907](https://github.com/Innei/sprightly/commit/58b79078543ab396f06fcdcf6284afb3f329e6c4))
* **deps:** update dependency @tanstack/react-query-devtools to v5.14.7 ([d98add8](https://github.com/Innei/sprightly/commit/d98add8e195155572c6b421fb9c4ec8f2e911b33))
* **deps:** update dependency daisyui to v4 ([#154](https://github.com/Innei/sprightly/issues/154)) ([77b019f](https://github.com/Innei/sprightly/commit/77b019fed95196999e9822795b3b32fe4c9312c6))
* **deps:** update dependency emoji-picker-react to v4.5.1 ([#78](https://github.com/Innei/sprightly/issues/78)) ([c4903ec](https://github.com/Innei/sprightly/commit/c4903ec5bfc565575ba0ce35561d71ea5f17d4bf))
* **deps:** update dependency emoji-picker-react to v4.6.0 ([#202](https://github.com/Innei/sprightly/issues/202)) ([b7ab626](https://github.com/Innei/sprightly/commit/b7ab626ff235acbe204b52a08bb79f1e8ce0ea3d))
* **deps:** update dependency marked to ^11.1.0 ([#184](https://github.com/Innei/sprightly/issues/184)) ([58176e9](https://github.com/Innei/sprightly/commit/58176e9e6717b908dd382f15872e61e1ab29b610))
* **deps:** update dependency marked to v10 ([2c81c8f](https://github.com/Innei/sprightly/commit/2c81c8fed2f45a1fb51f30cee179dc41e37c8776))
* **deps:** update dependency marked to v8 ([#73](https://github.com/Innei/sprightly/issues/73)) ([d728303](https://github.com/Innei/sprightly/commit/d728303a2833e282a54717d58ecbace6af0fc363))
* **deps:** update dependency openai to v4.10.0 ([#87](https://github.com/Innei/sprightly/issues/87)) ([bbfa7b6](https://github.com/Innei/sprightly/commit/bbfa7b6859762afbe0babf70dbcb65dce04b7777))
* **deps:** update dependency openai to v4.12.1 ([#103](https://github.com/Innei/sprightly/issues/103)) ([8a542a1](https://github.com/Innei/sprightly/commit/8a542a195a698f340a5f430d1dfd14f9247d773a))
* **deps:** update dependency openai to v4.7.0 ([#81](https://github.com/Innei/sprightly/issues/81)) ([e15f7c2](https://github.com/Innei/sprightly/commit/e15f7c2202f2745a320166695c72582224e6defa))
* **deps:** update dependency react-router-dom to v6.16.0 ([#77](https://github.com/Innei/sprightly/issues/77)) ([c763489](https://github.com/Innei/sprightly/commit/c763489df27f07e4ca06d6d0b24291f7d7723712))
* **deps:** update dependency react-tweet to v3.2.0 ([#195](https://github.com/Innei/sprightly/issues/195)) ([3f7d8bb](https://github.com/Innei/sprightly/commit/3f7d8bb3ffe385e49bd32710738f98c3874f28ac))
* **deps:** update dependency tailwind-merge to v2 ([#135](https://github.com/Innei/sprightly/issues/135)) ([493546f](https://github.com/Innei/sprightly/commit/493546f8e5061f5a925ac024f2bcddabc96c92b6))
* **deps:** update dependency tailwind-merge to v2.1.0 ([#172](https://github.com/Innei/sprightly/issues/172)) ([5aabcc8](https://github.com/Innei/sprightly/commit/5aabcc84169feba00b05b27cfa7871fcd4ffae33))
* downgrade daisyui ([fb080e5](https://github.com/Innei/sprightly/commit/fb080e5adcfe17c380e237603424b1389ebef069))
* emoji panel float popover ([5919712](https://github.com/Innei/sprightly/commit/59197124555fcc3df1698432c23792bd9a679375))
* escape xml, closes [#209](https://github.com/Innei/sprightly/issues/209) ([54bf8a5](https://github.com/Innei/sprightly/commit/54bf8a5b93eaeb56cdbd3f4b2b9ae87a6970c20d))
* favicon ([f9c969c](https://github.com/Innei/sprightly/commit/f9c969c89a5d1ef84d20b1978dc95c3868b8be91))
* favicon align and link parser ([a5156a0](https://github.com/Innei/sprightly/commit/a5156a05fe096898048acf88ee8f9fb490f3866b))
* folder typo ([421c7dd](https://github.com/Innei/sprightly/commit/421c7dd8ddac988aa0708def1856bf5e922e8d61))
* footernote anchor ([20b9fd2](https://github.com/Innei/sprightly/commit/20b9fd2882d4461aeb0e2e134540ca3929889830))
* footnote id ([#142](https://github.com/Innei/sprightly/issues/142)) ([f15caf7](https://github.com/Innei/sprightly/commit/f15caf747702a03c2999275ad97aa48da73a9015))
* friend avatar image fallback ([00577cd](https://github.com/Innei/sprightly/commit/00577cd8861bab31814861d0d8d964f923f70538))
* header drawer menu in mobile ([113876f](https://github.com/Innei/sprightly/commit/113876fe9b71046b511fc53dfe9e79a70ecffe95))
* header poppver ([6baeb47](https://github.com/Innei/sprightly/commit/6baeb473d449c6b0b5ddd13dcaed94699f074025))
* header spring transition ([b246808](https://github.com/Innei/sprightly/commit/b246808c586523e97e5e8428e2cdab9d2a5c730e))
* hide sign in ([211f951](https://github.com/Innei/sprightly/commit/211f951e2b535bde564517b67f1de4b61e1876a0))
* image type error ([07bee74](https://github.com/Innei/sprightly/commit/07bee74c88eaafe22e27e97e38eb93fe190c2784))
* improve link card if has color ([087fb8c](https://github.com/Innei/sprightly/commit/087fb8c909e97181a2821e3042495c8a5ddd41e6))
* indexDb in server ([0a1eea5](https://github.com/Innei/sprightly/commit/0a1eea51a65aacfbd9a844ba31592d8e5d8052df))
* jetbrains mono font, missing message app image ([#155](https://github.com/Innei/sprightly/issues/155)) ([2679e7b](https://github.com/Innei/sprightly/commit/2679e7bd5fa1515b3470a9fa0590f95003b05398))
* kateX format, closes [#156](https://github.com/Innei/sprightly/issues/156) ([46b799c](https://github.com/Innei/sprightly/commit/46b799c7de060fef9246201f3afad782d9257b15))
* **layout:** some layout in mobile ([#110](https://github.com/Innei/sprightly/issues/110)) ([43bf238](https://github.com/Innei/sprightly/commit/43bf238bf296da0b3b18f1250711dcf416d231eb))
* link parser for self link ([4d1dcda](https://github.com/Innei/sprightly/commit/4d1dcda99fa9465c866dd0b41f2266897933d8cc))
* live badge text color ([98f372d](https://github.com/Innei/sprightly/commit/98f372dc5b87c4a20f4d9de6bff33c4ff76e8cd4))
* lock file ([8a43cdb](https://github.com/Innei/sprightly/commit/8a43cdb0724052045b44911802abc477895095da))
* mask scroll hook ([3313c86](https://github.com/Innei/sprightly/commit/3313c864704522795ea2c5df87d9a6178ddc5e62))
* mermaid image size ([cabf459](https://github.com/Innei/sprightly/commit/cabf4595c1e89c964dcf90d0cd2f9e039f3f074f))
* mermaid theme ([c7064f3](https://github.com/Innei/sprightly/commit/c7064f3c3c1ab753d7e08cf46e431c39832788dd))
* next build ([e681827](https://github.com/Innei/sprightly/commit/e68182725cd206f73716c18ce6881eff5f91e600))
* node-fetch for ai ([0078d79](https://github.com/Innei/sprightly/commit/0078d79b4cf3c97c01392ab771bc4886e4130a81))
* note layout loading flash ([b4966e3](https://github.com/Innei/sprightly/commit/b4966e3639ee19d3c19bf3b60d925a99bf1a9bfd))
* openai fetch ([e0cab2f](https://github.com/Innei/sprightly/commit/e0cab2fef818735a100bcac4eae80e9702328c4e))
* optimize print ([c907401](https://github.com/Innei/sprightly/commit/c907401f6c946daa65716d6731cb53ac9fb9cdce))
* prevent scroll when search ([caa253c](https://github.com/Innei/sprightly/commit/caa253c51269ef3cf4063792dbc61314e184320f))
* preview hash ([465690f](https://github.com/Innei/sprightly/commit/465690fda03284f3d0fb4380076b40081fd2c23c))
* preview ping ([5089ead](https://github.com/Innei/sprightly/commit/5089ead6123dad39e0fc26413a684c75f4958e28))
* prose p last child style ([ec023c9](https://github.com/Innei/sprightly/commit/ec023c97140c66b0deb374f3768f5a3b35ddb90d))
* realtime preview render error ([a932c97](https://github.com/Innei/sprightly/commit/a932c97d7121c93acd5b947b46110c64ccc37c1f))
* remove css infinite animation to improve perf ([fe01bb5](https://github.com/Innei/sprightly/commit/fe01bb5262b74e1dfb84bd77848ebd109a4eea90))
* remove icon ([27eb1c6](https://github.com/Innei/sprightly/commit/27eb1c6aeff78b32d656af59fcf5c28e9a78eb58))
* remove re-decleare dynamic ([4086c32](https://github.com/Innei/sprightly/commit/4086c32d90082b637438fe678d1b08d3540d1b82))
* remove some post loading ([b3e4185](https://github.com/Innei/sprightly/commit/b3e418529d32891a9bb428d1bc02e7087ad46a60))
* rss structure ([9f0877b](https://github.com/Innei/sprightly/commit/9f0877ba459f5b5fab7cb55eb75ab74e75c2a255))
* scrollbar bug in dark mode ([#191](https://github.com/Innei/sprightly/issues/191)) ([7bf96c9](https://github.com/Innei/sprightly/commit/7bf96c9ca48ed301de110b832c0cac51c4ea671b))
* scrollbar color in light, closes [#185](https://github.com/Innei/sprightly/issues/185) ([9bf5e07](https://github.com/Innei/sprightly/commit/9bf5e07c3135b6f4ef64c95620b820c76c32f5bb))
* search content scroll ([1f1c20e](https://github.com/Innei/sprightly/commit/1f1c20e364e3968833c74a7cb331a5ab90e3b8ca))
* selection color in dark mode ([f289cc7](https://github.com/Innei/sprightly/commit/f289cc7e52bbf75a96013e05283ab62ceb6889df))
* self link parser ([cef8abc](https://github.com/Innei/sprightly/commit/cef8abc3ac04b416ff4c8151e3175be27dc7dfd8))
* sitemap data ([e0d2219](https://github.com/Innei/sprightly/commit/e0d2219b3267e0aa597d4cb1b0c52caae08f9f4f))
* socket errors ([#192](https://github.com/Innei/sprightly/issues/192)) ([95ae696](https://github.com/Innei/sprightly/commit/95ae696a1a77bab3010fc0968462bafcd8b3608e))
* some optimize ([45bad96](https://github.com/Innei/sprightly/commit/45bad96c4a44a151da5a8bdda973839ae63f7f1f))
* **storybook:** use hash router ([bb9e8a1](https://github.com/Innei/sprightly/commit/bb9e8a173dff6c0c086ecc09b116d0258edf8cb8))
* tag style ([d8a1c70](https://github.com/Innei/sprightly/commit/d8a1c7004f194fd4f0c1547b72cf7e66f0cadd5c))
* text shadow when select the anchor link ([9bd856a](https://github.com/Innei/sprightly/commit/9bd856a42af5494a52947706d858c5484571a0c5))
* toc mask overflow ([23dfb47](https://github.com/Innei/sprightly/commit/23dfb47d4252eaacf4e8881d1a378f4146a31691))
* toc scroller ([10cf466](https://github.com/Innei/sprightly/commit/10cf46633f60a7f4a20396d0f9967c82f97ffaf5))
* typo ([d3f1302](https://github.com/Innei/sprightly/commit/d3f13029bfe57f101379c353f53824a946bbcebf))
* typo & markdown selection action ([108d4c3](https://github.com/Innei/sprightly/commit/108d4c3e927e1c9c9304e41a0631f91958477d9f))
* ui update ([2763bc6](https://github.com/Innei/sprightly/commit/2763bc6e923e4ba4edc7a8480e2542489ef6800c))
* update script ([3c5425c](https://github.com/Innei/sprightly/commit/3c5425cc9c7950d16a6addef3e75563c445fa279))


### Features

* accent color transition ([fbb4ecc](https://github.com/Innei/sprightly/commit/fbb4ecc72b9d889d77338bb0971bf6583df2f2b6))
* ack read count ([c7489cb](https://github.com/Innei/sprightly/commit/c7489cb33fc425fd9a73781c9fba741aebd556a2))
* **activity:** add some app icons ([#128](https://github.com/Innei/sprightly/issues/128)) ([4e8bb2e](https://github.com/Innei/sprightly/commit/4e8bb2e09bf47e844d723efde2b9229233b29125))
* add `note` to md container parser ([45d8d88](https://github.com/Innei/sprightly/commit/45d8d881fe2837e5b92d95264eff02679efbe0e4))
* add `sitemap.xml` redirection ([f18ec28](https://github.com/Innei/sprightly/commit/f18ec2825c43c0b7e16679ad5205476f3e2750e6))
* add activity app icons ([#113](https://github.com/Innei/sprightly/issues/113)) ([bd31d5b](https://github.com/Innei/sprightly/commit/bd31d5be55d9b79a00ae2ed2193aa4ac224e1cac))
* add bottombar actions ([180449e](https://github.com/Innei/sprightly/commit/180449e9cd49673ffe122ce7dac15e1ee9973046))
* add Chinese translation and new app for activity ([#74](https://github.com/Innei/sprightly/issues/74)) ([f77241f](https://github.com/Innei/sprightly/commit/f77241fdf51de90e6323362ea9fda325746eb7ab))
* add collapse background ([4116060](https://github.com/Innei/sprightly/commit/4116060f4c5daa4f84218ceadf0031fb759cc9c7))
* add footnotes prompt ([#131](https://github.com/Innei/sprightly/issues/131)) ([c50d283](https://github.com/Innei/sprightly/commit/c50d28305bbfac8fb92b5ca5625724e10b3b4ac9))
* add link card spotlight ([fefdbee](https://github.com/Innei/sprightly/commit/fefdbee82673c1a8ef5a00e5f8157076e6f6b1d0))
* add mark timeline transition ([5831d55](https://github.com/Innei/sprightly/commit/5831d55283e816c6d7c0768e6d952ee14c973229))
* add nav on post meta bar ([6adae54](https://github.com/Innei/sprightly/commit/6adae540b27201243451d2103f68b5848e8f37f2))
* add pm2 log datetime format ([#193](https://github.com/Innei/sprightly/issues/193)) ([f1cc7d6](https://github.com/Innei/sprightly/commit/f1cc7d6a00a5698d4912757215a447027b49e2f1))
* adjust subscribe bell button, and fix [#137](https://github.com/Innei/sprightly/issues/137) ([503170d](https://github.com/Innei/sprightly/commit/503170d4e52a37575cfe648b229b1a561f5e2955))
* connect clerk to mx-backend ([7ccb7e2](https://github.com/Innei/sprightly/commit/7ccb7e28dc0e4a28ba18b81a8fae114e685dfbaa))
* Optimize Footnote Display ([#129](https://github.com/Innei/sprightly/issues/129)) ([e122241](https://github.com/Innei/sprightly/commit/e122241e23dba26980ef128677c0081dc7ce2963))
* optimize print styles and adjust font sizes ([2c29169](https://github.com/Innei/sprightly/commit/2c29169ad67676bd5ed0b4743c1d676f4c3ae408))
* parse github pr link ([50ddd75](https://github.com/Innei/sprightly/commit/50ddd75678ce2ec23d6b6408fac621c4f5c86ea3))
* support gfm alerts ([1f3d95c](https://github.com/Innei/sprightly/commit/1f3d95c1ac729545b8096d903e6815847ad01a57))
* support note header cover image ([42f30e2](https://github.com/Innei/sprightly/commit/42f30e2fb29fb1692537a904c4f7a089d965fbf3))
* supprot block katex ([65b4f64](https://github.com/Innei/sprightly/commit/65b4f64713ae9741d4a320fa97e3b439c75013e7))
* temporary change the route ([d674fad](https://github.com/Innei/sprightly/commit/d674fad9e10a82e495fd9c13daa84702019c7a65))
* to admin with token ([39d72e3](https://github.com/Innei/sprightly/commit/39d72e367702d2c33a270b529ca1045b75261644))
* update to next 14 ([03cf1c5](https://github.com/Innei/sprightly/commit/03cf1c510e9f282bc762ff009da94c2657eed19b))
* upgrade tanstack/query to v5 ([c5dc453](https://github.com/Innei/sprightly/commit/c5dc45315b24dae56456926eb1267d5a9598d139))
* vaul in mobile ([c919584](https://github.com/Innei/sprightly/commit/c9195846b298903a5d81796b59b1d3b2add2ee72))


### Performance Improvements

* disable accent transition in safari ([1280214](https://github.com/Innei/sprightly/commit/1280214e170175b50c852f9879413e24b1121ab7))
* use built in og font ([42f03ca](https://github.com/Innei/sprightly/commit/42f03caa3db521fdb4d7ff6c1992d6269bee8535))



# [0.2.0](https://github.com/Innei/sprightly/compare/v0.1.1...v0.2.0) (2023-08-30)


### Bug Fixes

* 404 file location ([7719258](https://github.com/Innei/sprightly/commit/7719258f6c2b781ca66c8f2701387d8a3b03964b))
* ai summary switcher ([fe28f5b](https://github.com/Innei/sprightly/commit/fe28f5b82b3759fdbdd93f8986042527a53d00e2))
* cleanup listener ([1076067](https://github.com/Innei/sprightly/commit/1076067f6a18c93ba7bd8c4dc602280881b620c7))
* comment with url closes [#47](https://github.com/Innei/sprightly/issues/47) ([974c804](https://github.com/Innei/sprightly/commit/974c8040ec30278633090d83bd6767084ffd8c8d))
* **deps:** update all non-major dependencies (minor) ([#50](https://github.com/Innei/sprightly/issues/50)) ([798f5e1](https://github.com/Innei/sprightly/commit/798f5e1217807d4abf9e7cb5643b66167e872142))
* **deps:** update all non-major dependencies (minor) ([#62](https://github.com/Innei/sprightly/issues/62)) ([2434c28](https://github.com/Innei/sprightly/commit/2434c28a21d22c0e1494e7cbabdedb2c1cf23f47))
* **deps:** update all non-major dependencies (minor) ([#68](https://github.com/Innei/sprightly/issues/68)) ([b6a9986](https://github.com/Innei/sprightly/commit/b6a998696ec800e2f56b8ce192766c9b5bcdf2fb))
* **deps:** update all non-major dependencies (patch) ([#67](https://github.com/Innei/sprightly/issues/67)) ([4f57274](https://github.com/Innei/sprightly/commit/4f57274d24cf0eb493d72e32309c3a8f9cda1f7e))
* **deps:** update dependency framer-motion to ^10.16.0 ([#65](https://github.com/Innei/sprightly/issues/65)) ([c8b5202](https://github.com/Innei/sprightly/commit/c8b520235661a39fff9bdbbc203917440307a385))
* **deps:** update dependency marked to v7 ([#59](https://github.com/Innei/sprightly/issues/59)) ([fddcbbd](https://github.com/Innei/sprightly/commit/fddcbbd2a6f6c5f47ce879c7d45616498c946674))
* **deps:** update dependency react-wrap-balancer to v1.1.0 ([#64](https://github.com/Innei/sprightly/issues/64)) ([24f7396](https://github.com/Innei/sprightly/commit/24f7396b6ff6ed4272c3cfce92e60e36b09745de))
* fallback source ([108b2cc](https://github.com/Innei/sprightly/commit/108b2cceeb73880f0c5082ff76e43bacf1679f78))
* github fetcher ([ed7f55a](https://github.com/Innei/sprightly/commit/ed7f55a85a1c9e537774982188c1bcd4447b4c4b))
* google translate crash entry page, fix [#51](https://github.com/Innei/sprightly/issues/51) ([a31248b](https://github.com/Innei/sprightly/commit/a31248bb085f9226c2cee60aecb1941b14dfb46b))
* header item navigation loop ([9e6ea98](https://github.com/Innei/sprightly/commit/9e6ea982d990a4c39ef067e58a1330138666e457))
* modal close when router change ([6b12fdb](https://github.com/Innei/sprightly/commit/6b12fdb293e0f59e66cea3218a0d974dd777fef8))
* note 404 ([a07d6eb](https://github.com/Innei/sprightly/commit/a07d6eb1b363519de5268ac7a082cd20f339cf7d))
* note blockquote style ([cb2b5e4](https://github.com/Innei/sprightly/commit/cb2b5e4b340ffedb765dc71849c9b662c8ee667d))
* notes page no data, page error ([#53](https://github.com/Innei/sprightly/issues/53)) ([17f3e1c](https://github.com/Innei/sprightly/commit/17f3e1c5e76938966ac1041cc557fe132ee71967))
* og api ([ffc2854](https://github.com/Innei/sprightly/commit/ffc2854759cfce78ebd7f92988f5fdfeb1261710))
* post list loading ([5f42521](https://github.com/Innei/sprightly/commit/5f425216d7c494c04dac7336ba6a8c8b1d101bfe))
* remove inline css ([60ce39d](https://github.com/Innei/sprightly/commit/60ce39d6e582d5f8119dd2295d14545460222ab1))
* remove unnessary logic ([e977674](https://github.com/Innei/sprightly/commit/e9776744e8a54762235e01a5fe8871f2b9913688))
* scroll to top when route change ([5d02bb8](https://github.com/Innei/sprightly/commit/5d02bb893f6026cbf25fc29006fbcb8e5bceeb73))
* some ui ([05fdadb](https://github.com/Innei/sprightly/commit/05fdadb7c4ad7124450a705e4e9a63f7e61b9275))
* some ui optimize ([#60](https://github.com/Innei/sprightly/issues/60)) ([3aea1cc](https://github.com/Innei/sprightly/commit/3aea1ccdf49b3a9dd3bdc0308dd3a78cdec47bf2))
* summary component in preview ([28c37bb](https://github.com/Innei/sprightly/commit/28c37bb4cf1a1341ced30b8dee52f7146adb8406))
* summary hydration error ([12187b9](https://github.com/Innei/sprightly/commit/12187b9e8206fd4de293c661b66041394c88fa0b))
* summary lang ([659b65c](https://github.com/Innei/sprightly/commit/659b65c7f9324ad73601a2b280c7f89f5bf284fd))
* table zebra style ([a8018dd](https://github.com/Innei/sprightly/commit/a8018dd0074e555749853f147fb134da3230beb4))
* toc tree and read indicator pos ([a136560](https://github.com/Innei/sprightly/commit/a13656030a9d306be059a730a18ace6515331b04))
* tpyo ([3de60e1](https://github.com/Innei/sprightly/commit/3de60e146dd6fc58f34be717086ad1be5433749e))
* typo ([2444dcd](https://github.com/Innei/sprightly/commit/2444dcdb789ca585337a4d241095640a524231db))
* typo ([ad4530a](https://github.com/Innei/sprightly/commit/ad4530a2ba74a791c6ddabd292ca1100be7b8fdb))
* ui improve ([56823e3](https://github.com/Innei/sprightly/commit/56823e3fe534adc0f1a7792c5ce9e42c97262cfc))
* update markdown-to-jsx ([a8eb26f](https://github.com/Innei/sprightly/commit/a8eb26f1f11de23e379f7e2c11e57c5d86b70637))


### Features

* add bilibili link parser ([e8ec06c](https://github.com/Innei/sprightly/commit/e8ec06c4a50da72a48457802e696529d89fa977d))
* add loading pages ([948e908](https://github.com/Innei/sprightly/commit/948e908d2e9599bd4790c842fbf3689bbad7fdd8))
* add vercel powered-by ([ad474a5](https://github.com/Innei/sprightly/commit/ad474a5acd67b0895c38a164416205b9d0d8cecf))
* add wikipedia icon ([9dc5536](https://github.com/Innei/sprightly/commit/9dc5536f920c61733426be8c5567c20e172aac73))
* ai summary widget ([877f04f](https://github.com/Innei/sprightly/commit/877f04fa50048e97038c55df305e5f97b27dc6f4))
* comment ui/ux improve ([de89cf2](https://github.com/Innei/sprightly/commit/de89cf2f47aff7aa972fed984210a4f8e76cac46))
* configure of sponsor ([19ba8a8](https://github.com/Innei/sprightly/commit/19ba8a8149645e38c54a734c3d6bd94fe8143c56))
* font switch ([f3840cf](https://github.com/Innei/sprightly/commit/f3840cff83c8344f2dea94829da3dd0194cb6538))
* home font size ([8042311](https://github.com/Innei/sprightly/commit/80423115ad8bc9768e744372c429995beea93b25))
* improve action button ([7011c69](https://github.com/Innei/sprightly/commit/7011c696d9c456c5c3eb73efcde9a2b3622e155b))
* markdown support tag ([784b096](https://github.com/Innei/sprightly/commit/784b09608cbfd86959e547664b694354a8d9d1d3))
* menu item style ([ea103c0](https://github.com/Innei/sprightly/commit/ea103c0f1e2493728b5dcee3b273fba77ffa1d8f))
* new 404 page ([ca284bc](https://github.com/Innei/sprightly/commit/ca284bcca3d1e37e8e4918c23af9b87c5cdc48b2))
* openai summary ([ba7a6d1](https://github.com/Innei/sprightly/commit/ba7a6d172fee45bb8800ac0f971e19784ae20c15))
* openai summary api ([082cf02](https://github.com/Innei/sprightly/commit/082cf023a25e5c91f96ca99b202d8175df7050bf))
* page route preview support ([a1a72ae](https://github.com/Innei/sprightly/commit/a1a72ae1171de73bbc391936bc3282ca558328c9))
* posts sorting and ordering, refactor fab ([6580fbd](https://github.com/Innei/sprightly/commit/6580fbd0759530e2aa9edadf5c83014032134627))
* preview page base on postMessage ([146c108](https://github.com/Innei/sprightly/commit/146c10874ab478eae7dd079215d488dde6338f9f))
* use activity event api ([a885462](https://github.com/Innei/sprightly/commit/a885462918a6e51cfd11c530120db36423bbbe9b))


### Performance Improvements

* comment dfs optimize ([4588bc8](https://github.com/Innei/sprightly/commit/4588bc8d20648ef947ae6904c9a1658c34d9198b))



## [0.1.1](https://github.com/Innei/sprightly/compare/v0.1.0...v0.1.1) (2023-07-27)


### Bug Fixes

* bili live api ([f74ce26](https://github.com/Innei/sprightly/commit/f74ce261fb4b55fce40939f96e30bd9ab5de37b8))
* composition input ([53b8769](https://github.com/Innei/sprightly/commit/53b87694b3127c11ff7684355779e4086a427f95))
* **deps:** update all non-major dependencies (minor) ([#43](https://github.com/Innei/sprightly/issues/43)) ([975be12](https://github.com/Innei/sprightly/commit/975be12d0b7bcd5b8dd9722331dd070edaf724bd))
* I really don't know how to write react and js ([60ee1aa](https://github.com/Innei/sprightly/commit/60ee1aa31fdce9b065069d9b23a33157ad6b3a84))
* kbd style ([e3cdcd5](https://github.com/Innei/sprightly/commit/e3cdcd507e464728ba98ec5d73644be3db647039))
* refetch comment after reply ([d5771c8](https://github.com/Innei/sprightly/commit/d5771c8b5f2bf14ca64cafb469cf855635f04594))
* replace emoji picker ([7d601e1](https://github.com/Innei/sprightly/commit/7d601e10f036ce74b05d02a04daba408c9fcbd47))
* router instance ([b3d834c](https://github.com/Innei/sprightly/commit/b3d834cb62dfbec0c20880852d029b157d46ebb7))
* selection color in dark mode ([e3b41d6](https://github.com/Innei/sprightly/commit/e3b41d60c20a4be1a571f128b3db20e7f188b481))
* selection in dark mode ([fe22426](https://github.com/Innei/sprightly/commit/fe224268a3dd2e746e115ef7d696e0408e71be40))
* some ui ([fcc07ec](https://github.com/Innei/sprightly/commit/fcc07ec2ab655435f6e3a518cb1dfbe1b0409813))


### Features

* add shortcut for open search ([a85e5b0](https://github.com/Innei/sprightly/commit/a85e5b067f1774700409db5aee8b2aadfcf474b7))
* github api reverse proxy ([c8258f6](https://github.com/Innei/sprightly/commit/c8258f65385839f8ac9beb2f8111737cfa5de8f1))
* go to dashboard ([95bde28](https://github.com/Innei/sprightly/commit/95bde28caba9a3dd7cab66502a577c0b68d93853))
* image compatible with MP4 ([f3516f4](https://github.com/Innei/sprightly/commit/f3516f42c5649aee47fbad012d193da0f71edbdf))
* og random color ([983d090](https://github.com/Innei/sprightly/commit/983d090f642b6cf65b42aafffa85eb24dabec2bf))
* show site avatar if logged ([d14362d](https://github.com/Innei/sprightly/commit/d14362d7d1afbb70be38682378a0754484857d47))
* style ([8cc7a86](https://github.com/Innei/sprightly/commit/8cc7a8616d5f5b9c9657c17b9bc1e0fbca02bf13))
* support customize accent colors ([6da8325](https://github.com/Innei/sprightly/commit/6da83255f4b3405c872119e4621aba7768d3e423))
* x icon ([3b8af39](https://github.com/Innei/sprightly/commit/3b8af39b660f5e0778b9637c70b81d34b815ef13))


### Performance Improvements

* I don't want to say anything just fxxk react again ([42cd176](https://github.com/Innei/sprightly/commit/42cd17608a1c6de4b47a6527f37fa81a6d49e441))



# [0.1.0](https://github.com/Innei/sprightly/compare/v0.0.1...v0.1.0) (2023-07-17)


### Bug Fixes

* activity refresh ([06332ad](https://github.com/Innei/sprightly/commit/06332ad58d8600ab186ee8c390c5a3fcb257c54b))
* add empty favicon ([70e26b5](https://github.com/Innei/sprightly/commit/70e26b585c953408ebe41688007ebc161dc72468))
* add icons ([acfc932](https://github.com/Innei/sprightly/commit/acfc932848135ecd30a35c9da06fb9fd9f646a49))
* add target for link ([cff3b72](https://github.com/Innei/sprightly/commit/cff3b7274e5bdbaddfa418f344ff83b6fe1fe7d1))
* aside will be covered by footer ([4d897d0](https://github.com/Innei/sprightly/commit/4d897d0b66a0ef353e41971d37eb7ff460c98314))
* build ([7cf8433](https://github.com/Innei/sprightly/commit/7cf8433206b6e0a892c19c4e3334b6d8c58038d1))
* build error ([ea0db85](https://github.com/Innei/sprightly/commit/ea0db85d2697ea5930803dab626d41f892d63d21))
* change timeline type refetch ([6c995d1](https://github.com/Innei/sprightly/commit/6c995d1642a4dd7dd2b6c167c39a2c37c005c538))
* cls ([3ef24d6](https://github.com/Innei/sprightly/commit/3ef24d6f272040006b1954d7b952ef62ec61ac9b))
* **deps:** update all non-major dependencies (patch) ([#33](https://github.com/Innei/sprightly/issues/33)) ([bee9bac](https://github.com/Innei/sprightly/commit/bee9bacae11b0e9196a1c8b79afc404fc1a5eabb))
* edge runtime ([dd9619f](https://github.com/Innei/sprightly/commit/dd9619f7558a5a44bce30e4194e2ac47ba55333d))
* error boundary ([27b6c0d](https://github.com/Innei/sprightly/commit/27b6c0d8f5f34828a4284955f39d334a8b4bcfc6))
* footer online count ([a4ddda6](https://github.com/Innei/sprightly/commit/a4ddda68ab400f05e160f38aed066acf505dc2bd))
* header cls ([7b6d1d5](https://github.com/Innei/sprightly/commit/7b6d1d5ad5c9c29d268e1e1faa0a0bcf27b4353d))
* header ml to fix cls ([ed59ba6](https://github.com/Innei/sprightly/commit/ed59ba640f6a199a17a2503902e949a82b2ca218))
* home friend screen ([bbe249b](https://github.com/Innei/sprightly/commit/bbe249b1984e420679aab4ac783b82cccced97c5))
* home layout ([f25de5e](https://github.com/Innei/sprightly/commit/f25de5e889d7436827397a46bd7acc24f79a7ce7))
* home page ([1bda357](https://github.com/Innei/sprightly/commit/1bda357a04407c6f1ae54ab75bff6fd3f6e8b5f8))
* iife ([4b7b8b1](https://github.com/Innei/sprightly/commit/4b7b8b1f0c7b49bec6815cadad43ff9493875243))
* link offset ([856396d](https://github.com/Innei/sprightly/commit/856396d17b53ae8c6e18a847ba5d06539eedcf3d))
* LinkCard callback url ([29179f4](https://github.com/Innei/sprightly/commit/29179f42a2890c7c2907a369780b262889d93301))
* mask ([eef4ddd](https://github.com/Innei/sprightly/commit/eef4dddaa32f762cbcc4782850fdee1e8ba57608))
* maybe fix framer bug ([99b36ce](https://github.com/Innei/sprightly/commit/99b36cee291f5681c43d0e1b651ca032f030441a))
* memo select fn ([b78a1ac](https://github.com/Innei/sprightly/commit/b78a1aca46961fa4c0526c050f60e1d1814c6da6))
* minify html ([c399c85](https://github.com/Innei/sprightly/commit/c399c85505a839275ef5a3f12c47e2a1efd76b60))
* nextjs call root `[slug]` will undefined ([ac97006](https://github.com/Innei/sprightly/commit/ac97006b3eb9dc215c26de8779d48430b1d8a662))
* optimize ([2c69af6](https://github.com/Innei/sprightly/commit/2c69af649ae68480b2391c7065aa792e1f3a1043))
* optional ([4d03672](https://github.com/Innei/sprightly/commit/4d03672666a09d46f9ed4d6160aca4d1e88837a6))
* peek modal shake ([06edd77](https://github.com/Innei/sprightly/commit/06edd777b1f7accd1b24986a6136e0b7416884af))
* read indicator style ([d71a319](https://github.com/Innei/sprightly/commit/d71a3193234da9a24bcb9cd2c11471b14782556e))
* reduce Activity update frequency ([d933691](https://github.com/Innei/sprightly/commit/d9336913a8ccaa0b1dfe82d73a2fc00842c59507))
* remove shiki vercel not support this ([dbf1dd4](https://github.com/Innei/sprightly/commit/dbf1dd4149e00a7587bb783912d46e5f09c65764))
* remove sonner ([9270e11](https://github.com/Innei/sprightly/commit/9270e1187b1574b09f6463b1c9ecec37b6dd9a53))
* restore script props structure ([90b9686](https://github.com/Innei/sprightly/commit/90b9686e8f97e643fcc8ea67679db30ee77fd50a))
* scroll to ([d06876d](https://github.com/Innei/sprightly/commit/d06876d98fb85c3d5d1ecd0af471b412ff8d9f5e))
* seo error ([ee3f5db](https://github.com/Innei/sprightly/commit/ee3f5dbb8f24e89aca78bb73a958716c25cd08ba))
* share modal cls ([48a5923](https://github.com/Innei/sprightly/commit/48a5923bc4686ecfdab2584d21166d0d3b4d0b2f))
* skew protection ([49cae51](https://github.com/Innei/sprightly/commit/49cae515fdf5d691fdbf4a21da0f76d631a471c8))
* some ui bug ([cad1038](https://github.com/Innei/sprightly/commit/cad1038e7b5a47f4ca3783f571a2cfc23cb966c1))
* some ui improve ([beac629](https://github.com/Innei/sprightly/commit/beac6296688a8d57652f83063e12db1c000d6a7b))
* some ui improve ([62d0923](https://github.com/Innei/sprightly/commit/62d09235720d5492409dcda6cc257d46327d85eb))
* some ui syles ([0ca38b4](https://github.com/Innei/sprightly/commit/0ca38b4fc97ecd4ec75b8debe43457dbd3762c0b))
* sonner theme and right aside in mobile ([ae2a3d3](https://github.com/Innei/sprightly/commit/ae2a3d3e17c3cd85c534c8aadc7f422bc2a4bc59))
* **style:** <main> fix the floating layer ([#38](https://github.com/Innei/sprightly/issues/38)) ([df8f376](https://github.com/Innei/sprightly/commit/df8f3765535aaeeaf821d3e0da0351a8116de6e4))
* temporarily disable theme switching ([6b1fcfd](https://github.com/Innei/sprightly/commit/6b1fcfd7d1fcea55520c4e355b38f59fa52f8dcd))
* toc ([0c3a6eb](https://github.com/Innei/sprightly/commit/0c3a6ebf2fd911e61c89ec8341c2d1db99cfe857))
* toc highlight ([a6fb8bd](https://github.com/Innei/sprightly/commit/a6fb8bde4ab1c85de02fdd0277df8245d40a43b2))
* try fix animate ([79398af](https://github.com/Innei/sprightly/commit/79398af0d3e3554afcda1a894a8909bc9c1e6ba9))
* type ([ebf35ab](https://github.com/Innei/sprightly/commit/ebf35ab3563aaf64bb9a063a0259aad532b109d8))
* type error ([c294e42](https://github.com/Innei/sprightly/commit/c294e4242253c06ea9e28a7917b80c4e2e58cf85))
* ui improve ([18e7bdd](https://github.com/Innei/sprightly/commit/18e7bdd8c568a52cf8f612b21ed704c6fd1f40d2))
* update ([c323e03](https://github.com/Innei/sprightly/commit/c323e03b322be50cf5ec31582825660ddf1df862))
* update ([6a5edc1](https://github.com/Innei/sprightly/commit/6a5edc18fc63c7b1fe0d381b741649ddb2b36357))


### Features

* add demo ([e575aa7](https://github.com/Innei/sprightly/commit/e575aa7f1eaae9bdb32e2727622256c697124924))
* add sitemap and rss support ([fc4e375](https://github.com/Innei/sprightly/commit/fc4e37500206ca362a857b0bd2b083e1477e9564))
* add summary skeleton ([1fed006](https://github.com/Innei/sprightly/commit/1fed006dab1b3ab1a9739a975d31897f61f6f409))
* add tracker ([1cfd22c](https://github.com/Innei/sprightly/commit/1cfd22cf37b5f62fc3812c135e9a6c0c2083feb7))
* animate and optimize ([8743465](https://github.com/Innei/sprightly/commit/874346503ed0a8e5f81f7bf5ef2a38fdf4b862f6))
* comment action button ([c034eda](https://github.com/Innei/sprightly/commit/c034eda0aa6100aba972ba8fb736c83da37a0898))
* disable fetch activity  when in background ([06a53a9](https://github.com/Innei/sprightly/commit/06a53a9c856e7ed63010c27a36e295e2b94977d2))
* edge config ([60417b6](https://github.com/Innei/sprightly/commit/60417b64626d0261c0997b00fee065100c07f238))
* edge config all ([d7714df](https://github.com/Innei/sprightly/commit/d7714df0fb7b84e899efadbe6a61fb17720d3b6a))
* embed codesandbox ([e672da5](https://github.com/Innei/sprightly/commit/e672da588a4c81d335d5118fca43fb7917a895b3))
* github file preview ([d1e7dbb](https://github.com/Innei/sprightly/commit/d1e7dbbb6510568838db90d907753e6d596c767a))
* glob macros support ([#41](https://github.com/Innei/sprightly/issues/41)) ([0f94f9a](https://github.com/Innei/sprightly/commit/0f94f9ad29b093cd5f20c739d89cf8d07a369a23)), closes [#39](https://github.com/Innei/sprightly/issues/39)
* go admin and editing ([c851c73](https://github.com/Innei/sprightly/commit/c851c73d84e5a9a1741a058d7d3835cb7fcff9e1))
* image optimize ([a711b0f](https://github.com/Innei/sprightly/commit/a711b0f08edd80b6eb65c46742c620882a8ecdbe))
* inline link support self parser ([756de9e](https://github.com/Innei/sprightly/commit/756de9e1651288f144d5af5136cd598cf91f5f6f))
* lcp optimize ([89ee9ad](https://github.com/Innei/sprightly/commit/89ee9adb758faa122302b09c4bf1b4e668424b60))
* motion ([c1c7372](https://github.com/Innei/sprightly/commit/c1c7372d0daf5dfd79e80c49403c390fd9bdd494))
* og ([5537cfb](https://github.com/Innei/sprightly/commit/5537cfb4c99d0bb0543d40c3e85f6f6cc6600c63))
* og ([0d62125](https://github.com/Innei/sprightly/commit/0d62125d85b55ef3b2c0935ee7d4ba8771c955c8))
* optimize ([fc6c00e](https://github.com/Innei/sprightly/commit/fc6c00e0bc5cf129643ee09f6cb5a0e39b23a33d))
* read indicator ([2331603](https://github.com/Innei/sprightly/commit/2331603776125a8919dece11918274291d9d6434))
* reference comment ([4a94877](https://github.com/Innei/sprightly/commit/4a94877c2261e75c4cf46c95a3e7de572bc8b99f))
* rich link ([52cd236](https://github.com/Innei/sprightly/commit/52cd236a50ac550297e3b607c370dd24d7d02461))
* rss render ([a47ad57](https://github.com/Innei/sprightly/commit/a47ad575ff4adc3561e4bf3d204e88c7ea249802))
* shiki ([6e4b5e9](https://github.com/Innei/sprightly/commit/6e4b5e966cbb7ada5d188036a4cb6422e69540e9))
* some ui optimize and next cache hit ([269189e](https://github.com/Innei/sprightly/commit/269189e104df1a7586d48c5dfafb879df2cc3973))
* source of gh-commit ([43d7e05](https://github.com/Innei/sprightly/commit/43d7e05a655530677678ef082091ce2eced5c0b2))
* support activity config ([5b5cb30](https://github.com/Innei/sprightly/commit/5b5cb302d9ca5849c50121004fd7b3066806e82e))
* thinking page ([66aa46d](https://github.com/Innei/sprightly/commit/66aa46d4ebc8e9ab9272e9a7d4d7f304d3f385f6))
* xlog summary ([16b3866](https://github.com/Innei/sprightly/commit/16b3866befd6ca113f4d3e32b9e6f56696f4491d))
* youtube link render ([c42aa8c](https://github.com/Innei/sprightly/commit/c42aa8ceab2cf1f153364c2b8b849c0d4fb260be))


### Performance Improvements

* memo `Activity` ([cc0cc47](https://github.com/Innei/sprightly/commit/cc0cc470dc90796a0d629a513400c8a2a348bf46))



## [0.0.1](https://github.com/Innei/sprightly/compare/3000d8049e64f42e4536e30a84e534c16726af5e...v0.0.1) (2023-07-06)


### Bug Fixes

* a11y ([8c9b062](https://github.com/Innei/sprightly/commit/8c9b062fe2c58c2a733f0a573f5ad89bc4625d56))
* activity ([d3b1e49](https://github.com/Innei/sprightly/commit/********************c149b9bd2664081c8b19))
* activity style ([bdb8a2e](https://github.com/Innei/sprightly/commit/bdb8a2ef2b63f76a3694d4597a39bf23909f5b5e))
* add favicon type ([8ce3b0d](https://github.com/Innei/sprightly/commit/8ce3b0dc6a8eef886ab0641ef1e7bbae73e08033))
* add layoutRoot ([555b7e2](https://github.com/Innei/sprightly/commit/555b7e28e462351ddf83e5e6bc19c22e5e4e6c9d))
* add margin for comment children ([59d1fd5](https://github.com/Innei/sprightly/commit/59d1fd587daf4330827e22a6704641a6506812a6))
* add todo ([37be829](https://github.com/Innei/sprightly/commit/37be829f0648b1f153edec6c1411d80ecadaae7a))
* adjust color ([6e6af1d](https://github.com/Innei/sprightly/commit/6e6af1db794f4acbe356dd50329773715cf595c9))
* adjust footer style ([af543ec](https://github.com/Innei/sprightly/commit/af543ec34233f12187c5ba93429580669dd0096a))
* adjust toc ([e6241b3](https://github.com/Innei/sprightly/commit/e6241b381152455d66d489393dc5e2eba84866f6))
* api endpoint ([72d1f52](https://github.com/Innei/sprightly/commit/72d1f52eeeb8fa58b02783bb0cd0fd33e331d508))
* banner inside markdown style ([a734909](https://github.com/Innei/sprightly/commit/a734909d89a89631e4816cef8a5aff2f9a395dc3))
* bg color ([c9c678d](https://github.com/Innei/sprightly/commit/c9c678d3a51d6d10c1153bb64902f63fb526854f))
* build ([44aceca](https://github.com/Innei/sprightly/commit/44aceca3298491af380719b86f79fff6d515a247))
* case ([40b53f2](https://github.com/Innei/sprightly/commit/40b53f2c7ffd815675aad70b676bacc06da90c3d))
* cleanup ([4e6496a](https://github.com/Innei/sprightly/commit/4e6496a7937bca54a1e171e75e2f301435a86860))
* clerk color ([2f39e0a](https://github.com/Innei/sprightly/commit/2f39e0a9a9c92ca5afdda6269b398c20872ff16b))
* color ([29b9cc8](https://github.com/Innei/sprightly/commit/29b9cc80c8ef93d6d4666d9494e547eee0720744))
* comment box ([22f4020](https://github.com/Innei/sprightly/commit/22f4020ccd5e5be02233993db28ac6ec1bba7091))
* css style ([32edc0b](https://github.com/Innei/sprightly/commit/32edc0b4eb637977a3f574b087c22d372b301b73))
* **deps:** update all non-major dependencies ([bc9f8ba](https://github.com/Innei/sprightly/commit/bc9f8baabaa91099ce657d69f5b7faccd3445d97))
* **deps:** update all non-major dependencies ([#9](https://github.com/Innei/sprightly/issues/9)) ([e10d01f](https://github.com/Innei/sprightly/commit/e10d01fcf4ae139e08fba76b51278e6feba8026a))
* **deps:** update dependency axios to v1 ([#7](https://github.com/Innei/sprightly/issues/7)) ([d5efeca](https://github.com/Innei/sprightly/commit/d5efeca3c274b63150e2fbd79c42768e1d0e5c77))
* **deps:** update dependency immer to v10 ([#8](https://github.com/Innei/sprightly/issues/8)) ([3000d80](https://github.com/Innei/sprightly/commit/3000d8049e64f42e4536e30a84e534c16726af5e))
* **deps:** update dependency react-error-boundary to v4.0.10 ([b112b67](https://github.com/Innei/sprightly/commit/b112b6787bd3132822a72f3b1058aa99078b359b))
* donate overlay center ([ce48b33](https://github.com/Innei/sprightly/commit/ce48b33bc17139473f90186f36c650a73a8c732f))
* extend the expiration time of xlog ([363920c](https://github.com/Innei/sprightly/commit/363920c5d769ff7e71278a1a630e965ad07eccb6))
* fab bg color ([e4fdd9a](https://github.com/Innei/sprightly/commit/e4fdd9a2be5298689346af49806128003e9e4e14))
* fab style ([fc5ca16](https://github.com/Innei/sprightly/commit/fc5ca16c59f8a50d7d276f3bfed2a6249200d00c))
* first paragraph style ([36ecafb](https://github.com/Innei/sprightly/commit/36ecafb0083f158448b5c21d812835c9be034bd0))
* footer nav ([c633964](https://github.com/Innei/sprightly/commit/c6339640957cbeda7ff78ffebb26304e05f5184a))
* geo ([90877db](https://github.com/Innei/sprightly/commit/90877db8ff0c9acf2632799bac9cdc56e67c20ea))
* hash style ([ee10476](https://github.com/Innei/sprightly/commit/ee10476978312b1ebdf4261a31a5a381ba3844ff))
* header drawer content ([969a321](https://github.com/Innei/sprightly/commit/969a321c4adcbe343672e057d99716479f61cd65))
* header icon ([2c56c29](https://github.com/Innei/sprightly/commit/2c56c290fc75d9a0e5be212f45beaccfab8d89f8))
* heading anchor ([a40f4f1](https://github.com/Innei/sprightly/commit/a40f4f19f258fec2c6235e83026cdc810d7b979e))
* heading style ([6fc8f10](https://github.com/Innei/sprightly/commit/6fc8f105444dbcfd83966ccd4503df365d733702))
* image margin ([f8902a6](https://github.com/Innei/sprightly/commit/f8902a62120b3974e9d063a84291449bab162652))
* image preview zoom ([cabf021](https://github.com/Innei/sprightly/commit/cabf021e26c2500f21894afe91a8c56783e541a2))
* lcp ([7b86239](https://github.com/Innei/sprightly/commit/7b86239296a03c352c28e6d75bfc7654193bcbf1))
* lint ([a7617e1](https://github.com/Innei/sprightly/commit/a7617e1ccbc2d1da16bde73ea7f4d4dfdcaa02a5))
* loading class ([60ad851](https://github.com/Innei/sprightly/commit/60ad851cf44e3fe4d19b7b685bfb520324bc7d52))
* meta pos ([eee3915](https://github.com/Innei/sprightly/commit/eee39151873e7cfefec28063ffe568eef73e7508))
* mobile view ([7504ce9](https://github.com/Innei/sprightly/commit/7504ce9e7d0adcec6255dd2909b90a6c8986e013))
* not allowed style ([038ed9c](https://github.com/Innei/sprightly/commit/038ed9c6752fde1f044d76db8801057f89b48bae))
* note page render lcp ([2b0f867](https://github.com/Innei/sprightly/commit/2b0f867e7a9d2a62be2e09a47c9f5233352e6127))
* note set current id in error page if 403 ([40fa00f](https://github.com/Innei/sprightly/commit/40fa00f1f3bc4d1c3840777e90c4b9516f6a4562))
* note side width ([46bb0d0](https://github.com/Innei/sprightly/commit/46bb0d05ca59bcc31d7c3ce0fa90aa0e9de53e8c))
* peek link ([703cde8](https://github.com/Innei/sprightly/commit/703cde8a5d0c05c8fa144b8bdd718ddc7eb634fb))
* post layout ([f3008a6](https://github.com/Innei/sprightly/commit/f3008a6e4f9e2097b0566936f0724bb25d9adc08))
* pref ([8f736d8](https://github.com/Innei/sprightly/commit/8f736d861c4d6f00112b0fb1dca5b8295ef1dfdc))
* proj header ([2062db9](https://github.com/Innei/sprightly/commit/2062db921d28f278a2669338f3b6d7fb2373a053))
* query ([5d5c759](https://github.com/Innei/sprightly/commit/5d5c75905fb0b1846eb1f43085a8c176b9847874))
* read indicator style in note toc ([d36a8d2](https://github.com/Innei/sprightly/commit/d36a8d2333563135aba9bcb0387ef8c7d124fa3a))
* reduce nest children ([18d1dc6](https://github.com/Innei/sprightly/commit/18d1dc604789e19d496c49b6b822c2b3584ad837))
* remove rq hydrate ([474dd8b](https://github.com/Innei/sprightly/commit/474dd8b2f1bcd202cfc50d671b7abcd7d8e9b0f5))
* remove upstash ([b579a80](https://github.com/Innei/sprightly/commit/b579a80c797a48f44abf28cf4f18fc9b75867a79))
* remove vercel/kv ([96b84a6](https://github.com/Innei/sprightly/commit/96b84a67132c7a5e2a501c1f1efa03dc99ba9253))
* request scope queryclient ([38f1bf6](https://github.com/Innei/sprightly/commit/38f1bf6f6876bd909b9c9da17390667cdcbd473f))
* sentry auth token ([3cb10a4](https://github.com/Innei/sprightly/commit/3cb10a43ea55e47878f769f837c33ef499051c4a))
* seo ([eae3e03](https://github.com/Innei/sprightly/commit/eae3e03fb7e0100d6a8ee8615ad741a5e96e81ee))
* skip peek on mobile ([b3f7cc6](https://github.com/Innei/sprightly/commit/b3f7cc6be0c9bf70cad05f47af1178643080465d))
* some ([39138b0](https://github.com/Innei/sprightly/commit/39138b0708fa59353bdd52ae2169bb607ab62703))
* some optimize ([119620d](https://github.com/Innei/sprightly/commit/119620d09c495f0814b029cb5fdfbffb21b7cbc6))
* some post style ([c424e54](https://github.com/Innei/sprightly/commit/c424e542b94be6d507350c8da490d8b9c81afbd3))
* something ([d4a9838](https://github.com/Innei/sprightly/commit/d4a983874fd688a1b9fb55009707e44061df6cc4))
* style ([0360a8e](https://github.com/Innei/sprightly/commit/0360a8e608adf3ecbdaba398b8e001c2b140a9b5))
* styles ([2e8f22b](https://github.com/Innei/sprightly/commit/2e8f22bed47998239159e32537b1c298b042c5e8))
* text overflow ([ff1183e](https://github.com/Innei/sprightly/commit/ff1183ede0fe5d3b01c6391514df67720f68c3bf))
* theme switch border color ([b5ceba7](https://github.com/Innei/sprightly/commit/b5ceba723ce554e57f09e34064a06b7d987003b1))
* thing ([8b8dede](https://github.com/Innei/sprightly/commit/8b8dede4b4cc9c2c7e70a4c3c0fb4e7d05e55bfd))
* to top ([7f6fe84](https://github.com/Innei/sprightly/commit/7f6fe84f11122378aa5aec024cd10220080ad8f7))
* toc ([1f3fa9c](https://github.com/Innei/sprightly/commit/1f3fa9c6da3e88f6b39d584df79b02d199eb6934))
* toc hash ([90fd78b](https://github.com/Innei/sprightly/commit/90fd78bd94fc5923dd2cc2bc8517b8bb72722d31))
* toc overflow ([39e1807](https://github.com/Innei/sprightly/commit/39e18078a418cf954b16cdcdb93ab782a772dd7f))
* toc width ([a2c2e93](https://github.com/Innei/sprightly/commit/a2c2e93c72826d15d2fe723e14314f7bcc4ea653))
* tooltip ([5b926e3](https://github.com/Innei/sprightly/commit/5b926e38e06bcfcfeb5c13edaba8fee3e4d56beb))
* ts error ([6b91a92](https://github.com/Innei/sprightly/commit/6b91a92a8a0a2dddf13057e1708428a620c27515))
* type error ([e60cbc9](https://github.com/Innei/sprightly/commit/e60cbc9a6cdac7aecac1c56528da2b64c3f26e1d))
* type error ([0b86e00](https://github.com/Innei/sprightly/commit/0b86e004cb5fd8c025e49a8fe1b225950569ccbd))
* ui ([94bd766](https://github.com/Innei/sprightly/commit/94bd76683fcae98622a516bc4cd4ca74912d3cf0))
* use motion ([fc6f268](https://github.com/Innei/sprightly/commit/fc6f268f3b08233583512e92e65a5e835f100ba4))
* vercel build ([dc7345e](https://github.com/Innei/sprightly/commit/dc7345ed1f2085b4ad6523439aa6a839fe2e4e36))


### Features

* [@fz6m](https://github.com/fz6m) teaching and review suggestions ([1c74c89](https://github.com/Innei/sprightly/commit/1c74c89142d92c68e6bee491f2c3ebed7ff8a872))
* accent-focus color ([5fffb75](https://github.com/Innei/sprightly/commit/5fffb75c7bb11fec159b18b43e4593c65f5ee640))
* activity ([7dc7d7d](https://github.com/Innei/sprightly/commit/7dc7d7d3455fced49281657fd8836e2973c99689))
* add analyze ([aa1891a](https://github.com/Innei/sprightly/commit/aa1891aef037db733417d0aeb748ce8b3b5568c7))
* add ip ([8f62651](https://github.com/Innei/sprightly/commit/8f62651dc0baae6ff13c58fba7e33ab38e7dddd7))
* add media info ([995b04f](https://github.com/Innei/sprightly/commit/995b04f7557092cd8acc39d11791c7dbf21d431c))
* add typora ([809c616](https://github.com/Innei/sprightly/commit/809c616f3601df4e4903fee643221bcf7c7952f6))
* add vercel cli ([190538c](https://github.com/Innei/sprightly/commit/190538c760bea161fde3eead27f94ddb74912083))
* adjust gap ([681f3db](https://github.com/Innei/sprightly/commit/681f3db2216a8a0d9298af31dc56cf6c56056d4f))
* allow banner markdown ([485d49e](https://github.com/Innei/sprightly/commit/485d49ee9e5cafefa71473449441452ccbf22be8))
* alyout ([65dd6cd](https://github.com/Innei/sprightly/commit/65dd6cd4087576c772f7d440aed398fc508b15a9))
* apply friend modal ([32828d3](https://github.com/Innei/sprightly/commit/32828d33a1a6d93cd5e04b01dfe37dd2e5a9817e))
* ban copy wrapper ([c1bf7d6](https://github.com/Innei/sprightly/commit/c1bf7d6e97cb8a3df14d85cffbfabb3f56fe0db8))
* category page ([fef1be0](https://github.com/Innei/sprightly/commit/fef1be0781cf1e56efd2964d431340efabb223df))
* code block ([0297ed5](https://github.com/Innei/sprightly/commit/0297ed5abcca363b82054b72726372cc556a31f9))
* comment allowcomment ([b918393](https://github.com/Innei/sprightly/commit/b918393f3c5d4f738d020f49d998ea1fb0f74d3d))
* comment box ([8f99190](https://github.com/Innei/sprightly/commit/8f991900e8fd590a2f145f0174b1293fd440fbc1))
* comment box init ([f28b398](https://github.com/Innei/sprightly/commit/f28b398dd5df1ac5f3e2eb6c755ad3249ba6e887))
* comment box init ([032bf39](https://github.com/Innei/sprightly/commit/032bf398bc051c07e28f4302f69c42f690412e69))
* comment box legacy ([7f383c9](https://github.com/Innei/sprightly/commit/7f383c9ad0146e636e84f6fbf4faa47cb83b8085))
* comment list ([98837de](https://github.com/Innei/sprightly/commit/98837deae0bab319af3b4b28c2b95e998e30f030))
* comment preference ([118fa88](https://github.com/Innei/sprightly/commit/118fa88477fa582dc08f288d860056642e3288f3))
* comment reply support ([8c1a631](https://github.com/Innei/sprightly/commit/8c1a631692464f5e73a1d2fd202b27b727df16e2))
* comment skeleton ([9585c1e](https://github.com/Innei/sprightly/commit/9585c1e06fb77bbc85372c9c4882ab5ce2d47e1c))
* comment transition ([36ae9bc](https://github.com/Innei/sprightly/commit/36ae9bc8d9e820fb28b10ba38e00b1d42844d434))
* comment ui ([35b61db](https://github.com/Innei/sprightly/commit/35b61db122d5b9af6cfb3711776a66860981c370))
* design ([655340e](https://github.com/Innei/sprightly/commit/655340ec95a2ab2f8fb388ebe6db170fb01a0cd3))
* donate button ([e52bd3f](https://github.com/Innei/sprightly/commit/e52bd3fdf7ce659cf3d1cf60295b758203860551))
* donate button overlay ([0c73ce0](https://github.com/Innei/sprightly/commit/0c73ce06f596e5cecebba25859729ce8e22c92da))
* emoji picker ([a6bdd3e](https://github.com/Innei/sprightly/commit/a6bdd3e260dbdd39bdb362f16faeeb518c383a93))
* fab accent outline ([0a6f7d6](https://github.com/Innei/sprightly/commit/0a6f7d6c15e3b5cd422ef2cd4070b94afaaae309))
* gallery & storybook ([c268488](https://github.com/Innei/sprightly/commit/c268488a9a0e8df6f736045dbf9b61f3417b6552))
* header ([30d5e52](https://github.com/Innei/sprightly/commit/30d5e5206448aaa6ad4316d337810bb166f82861))
* header accessibility ([36c3460](https://github.com/Innei/sprightly/commit/36c3460e2c146f61784a18512ccc4745eb87eb28))
* header drawer ([96fe3fa](https://github.com/Innei/sprightly/commit/96fe3fa22f6af30a1fbc95e69b2fe3c1664797cb))
* header layout ([9805007](https://github.com/Innei/sprightly/commit/9805007b492e3c34b2744841ca2cf088bd3c60c0))
* header meta mobile design ([e062711](https://github.com/Innei/sprightly/commit/e06271118886501c460fabfc09e932d7caea757a))
* header meta support ([4f3aba4](https://github.com/Innei/sprightly/commit/4f3aba4dbf2c8b5be05620abbe90c672a3f24e59))
* header popover ([f3702a4](https://github.com/Innei/sprightly/commit/f3702a4d99b8ff3c8b42f26059de7f646386abc7))
* header responsive ([7a87b93](https://github.com/Innei/sprightly/commit/7a87b936d2cb9d47bb12d971b6a9a62da07e1558))
* header sub menu ([6d7c973](https://github.com/Innei/sprightly/commit/6d7c9738aa7a014e295db2b7a0e1e7252fee037a))
* header transparent hoo ([91dfc54](https://github.com/Innei/sprightly/commit/91dfc540667024d8e33b80cff8c63b859fe28773))
* header with avatar ([aeaeacd](https://github.com/Innei/sprightly/commit/aeaeacd59cf80f580deb112ce5f6a75af01f7a8f))
* hero ([d03939d](https://github.com/Innei/sprightly/commit/d03939d99af04dbb10038e87d02aca4ee449ea1a))
* home layout ([9964c14](https://github.com/Innei/sprightly/commit/9964c143c676b89bcf9e6f2abd6748842ed07843))
* home page init ([8ec036d](https://github.com/Innei/sprightly/commit/8ec036dac8f8212d151c78bd0311a6813d528a57))
* image init ([9e6cb36](https://github.com/Innei/sprightly/commit/9e6cb368341b437f6b188cd15348eb37ed74c684))
* image transition ([efea201](https://github.com/Innei/sprightly/commit/efea201c9bad5ad6f3c448f7472290a48a7634b9))
* improve print mode ([f37a427](https://github.com/Innei/sprightly/commit/f37a4276f141b109c150b79e871653e27d23cef0))
* init markdown component ([fcbbb86](https://github.com/Innei/sprightly/commit/fcbbb8652f00d9c9988aaa1c69f6f481c5406c2e))
* init query ([4dea6e4](https://github.com/Innei/sprightly/commit/4dea6e400e0458246a2ebc93df45ce25cfe70e5a))
* layout ([bb46cf5](https://github.com/Innei/sprightly/commit/bb46cf530bfd371ba62a1fc774d857391bfd028c))
* link card ([7a4b87f](https://github.com/Innei/sprightly/commit/7a4b87f78ef76ecfe616086fe75f84c5446cab2f))
* linkcard peek ([873597f](https://github.com/Innei/sprightly/commit/873597f59f44d96b630c6075de4dcc60f750923a))
* live status ([37fedf4](https://github.com/Innei/sprightly/commit/37fedf410ee3bed7fc923df1aaed7cc49daa18cd))
* login page ([02c5e9e](https://github.com/Innei/sprightly/commit/02c5e9ef832cc6086b5d14fcb452cca712b1b5ce))
* markdown extra component ([525cbc0](https://github.com/Innei/sprightly/commit/525cbc033440681c59a6c233b965c93336202ed3))
* mobile optimize ([b9ef2d2](https://github.com/Innei/sprightly/commit/b9ef2d2e7fd475be307bac2ad08ac0dceb07e2ba))
* mobile optimize ([868bba7](https://github.com/Innei/sprightly/commit/868bba7e067b52d906320d3f5917727ef723f655))
* modal stack ([b3eec57](https://github.com/Innei/sprightly/commit/b3eec57dd4fa24a4478bbfd736e453a0fd9450c9))
* modified info ([2393a99](https://github.com/Innei/sprightly/commit/2393a993232520c4a525e6e1915b05a730bae785))
* new image transition ([674345e](https://github.com/Innei/sprightly/commit/674345e87a83d61228dae04748b35889e3afe264))
* normal container ([35dcc64](https://github.com/Innei/sprightly/commit/35dcc643badea5c665bcb8c1422d5f339296a9a1))
* note ([6f187ec](https://github.com/Innei/sprightly/commit/6f187ec8798ec5094e5aeab1fef973fe4aaa225b))
* note action bar ([dffd250](https://github.com/Innei/sprightly/commit/dffd25099a88f19b4416efbbe64bae846e251c54))
* note header banner ([e7b9a3d](https://github.com/Innei/sprightly/commit/e7b9a3dbf7235dd7032952f61d4fade56ad6ef3c))
* note layout ([464b9e9](https://github.com/Innei/sprightly/commit/464b9e9a1cb90b2a34812f48fcd63b65f6944148))
* note layout right side ([c4f80a3](https://github.com/Innei/sprightly/commit/c4f80a3f388f8fc18a133766b01a350d3210ff3e))
* note left bar dont sticky when scroll out paper ([571c049](https://github.com/Innei/sprightly/commit/571c04939add202fcf3e18cb9404bbc2014865cc))
* note meta icon ([f413e23](https://github.com/Innei/sprightly/commit/f413e239b398fd091fd448b8722995b6a06af5a1))
* note redirect ([0151192](https://github.com/Innei/sprightly/commit/0151192be09fdb8380b05100a5fb965f26c9ca52))
* note side share ([7cdc0e7](https://github.com/Innei/sprightly/commit/7cdc0e7a34192abe22d25ce813beb469b15eaa84))
* note timeline ([3e5fa42](https://github.com/Innei/sprightly/commit/3e5fa42e4af352a7f3e1904d55b700124b971b4e))
* note update event ([aeaedfe](https://github.com/Innei/sprightly/commit/aeaedfecc3e24068caf85546ad40a6fa1b76362d))
* notelayout ([d191cb2](https://github.com/Innei/sprightly/commit/d191cb24170e0083d700bef819920bce6842d006))
* notelayout and toc ([87ab8a2](https://github.com/Innei/sprightly/commit/87ab8a2e222454422aec9b2a1d0f832f48755f44))
* optimize ([e13dd8c](https://github.com/Innei/sprightly/commit/e13dd8c4f5e5351f6808d23b02c0a299c60a427b))
* optimize icon ([5c9f98d](https://github.com/Innei/sprightly/commit/5c9f98da1029776cbae4f1073362dca3ca31aaea))
* optimize ux motion ([a28db6e](https://github.com/Innei/sprightly/commit/a28db6e779f9e55346dc700632e802794d7ca608))
* page paginator ([a4b28a1](https://github.com/Innei/sprightly/commit/a4b28a132a3ec6f79b1aa8b8e7601e17984f7d3a))
* peek ([e1b0b57](https://github.com/Innei/sprightly/commit/e1b0b57aaea0eec1b695c4f1961297b42b935044))
* peek link ([df5d8ba](https://github.com/Innei/sprightly/commit/df5d8baeb2ddaee4d04c6bd67cd07d5b031fa388))
* post copyright ([b4f9f98](https://github.com/Innei/sprightly/commit/b4f9f985586ca7b6558933b079a1371d0c74966a))
* post detail page ([456b9d5](https://github.com/Innei/sprightly/commit/456b9d51bac42ea0a4d342e3a825f63d192db618))
* post list ([00928bc](https://github.com/Innei/sprightly/commit/00928bcb330620f8bac8a0819b6e3b3a419b38f2))
* post outdate info ([50a7735](https://github.com/Innei/sprightly/commit/50a7735ffce17d16f30520af926f06d83225a9e8))
* post pin state ([fb46804](https://github.com/Innei/sprightly/commit/fb46804f636cc5ecfcef48e156646d6675a98104))
* post related ([78efb88](https://github.com/Innei/sprightly/commit/78efb8842e1635d44f0e0ceee408db7499a800cc))
* proj page ([c32a5a0](https://github.com/Innei/sprightly/commit/c32a5a061b575b365841da07b9e6ae88073ec144))
* read indicator ([0130eee](https://github.com/Innei/sprightly/commit/0130eeebbd5dd8cb1a2925a46be29f63341f861e))
* read indicator ([30d5395](https://github.com/Innei/sprightly/commit/30d5395f723d05ff4cac65789ef6a5213733a859))
* replace regular meta icons ([279f1cc](https://github.com/Innei/sprightly/commit/279f1cc36676f79f5ea4388b4af20e4fa4c73862))
* route builder ([9ad87c3](https://github.com/Innei/sprightly/commit/9ad87c362330061ac06160c6608443cd2aa5d0e4))
* say view ([e6ba8b2](https://github.com/Innei/sprightly/commit/e6ba8b23a7b5122ade6b0b6e344de3790ff7a150))
* search panel ([9e80d70](https://github.com/Innei/sprightly/commit/9e80d706f5af26ffa11545d775a3ad9dc00f4200))
* share modal ([c6ce103](https://github.com/Innei/sprightly/commit/c6ce10327816c49eab51291f49d38f167543f8da))
* should do hydration ([538f0a2](https://github.com/Innei/sprightly/commit/538f0a272779563bd43c5154914b03d64d1c0f69))
* socket event ([1423459](https://github.com/Innei/sprightly/commit/****************************************))
* something adjust ([72c1f38](https://github.com/Innei/sprightly/commit/72c1f387eb716265007be1bf59582f1e5ea048cd))
* support clerk ([d8a7538](https://github.com/Innei/sprightly/commit/d8a75383ab25f967381a5b643f8ac9829bec745d))
* support socket ([fa8d704](https://github.com/Innei/sprightly/commit/fa8d704df19d775b4c291bfb016dbb303c470cfd))
* support xlog component ([6a0c2b9](https://github.com/Innei/sprightly/commit/6a0c2b9db6958db6a47c88c38d9a8b189234f47b))
* switch button ([ad17dfa](https://github.com/Innei/sprightly/commit/ad17dfaf468c01d595fc64d6eaaf888f49162663))
* tag modal ([41f40ef](https://github.com/Innei/sprightly/commit/41f40efdc5cabc28d9ca2f31625134ff49eddf06))
* theme color ([302db77](https://github.com/Innei/sprightly/commit/302db77a21215ef76a290856f5bbd63ea4c0a582))
* theme switcher ([bb15bba](https://github.com/Innei/sprightly/commit/bb15bba8b6299898003036ca5224b15fd81b6efc))
* timelint init ([fc1b11d](https://github.com/Innei/sprightly/commit/fc1b11db89eb02e7a887eeedfdb8760f24b42685))
* to top ([3e0c815](https://github.com/Innei/sprightly/commit/3e0c8157a67d90cf347e6bde8671280aa12670c8))
* to top ([3742f6c](https://github.com/Innei/sprightly/commit/3742f6cedc0645cb76f4b996012ad13307908288))
* toc item smooth indicator ([d459308](https://github.com/Innei/sprightly/commit/d4593085b3764b63779bdf19a36463ecf21ca84f))
* toc modal in mobile ([5c0f853](https://github.com/Innei/sprightly/commit/5c0f853929b86a6f4ab6c4b5063fec1e33f1f661))
* topic pages ([d2af6db](https://github.com/Innei/sprightly/commit/d2af6db7e0b3c36a919f678171514df4b000006c))
* tweet embed ([5abb757](https://github.com/Innei/sprightly/commit/5abb757c8bd58745b5ffeeed4e811f7b0e53d155))
* ui improve ([ebd4ed1](https://github.com/Innei/sprightly/commit/ebd4ed1181f3ece64668327272b79e3d8ad7679a))
* uikit color palette ([f3753fe](https://github.com/Innei/sprightly/commit/f3753fe699d563a9778fa3800d1546120afe0262))
* update ([3277ed8](https://github.com/Innei/sprightly/commit/3277ed81aff7de27d2c042242290680ec41d037c))
* update blockquote style for note ([a5de44b](https://github.com/Innei/sprightly/commit/a5de44b58a3081b5e0e765b5a1d211e341c34ae9))
* update hero ([24d06ba](https://github.com/Innei/sprightly/commit/24d06ba10405a639c5faf147c966f0a5e97d9d09))
* user login from icon ([a72bfc9](https://github.com/Innei/sprightly/commit/a72bfc96119063805145c4a3fa1772273d1df161))
* view transition ([b5b2b04](https://github.com/Innei/sprightly/commit/b5b2b046d63851a446d4dfc4ab8decd7429e2087))


### Performance Improvements

* re-reduce router change re-render ([f0f529f](https://github.com/Innei/sprightly/commit/f0f529f2eefc58bd8ea1dd445b001a628c57ef83))



