<template>
  <section class="py-16 bg-base-200">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">站点统计</h2>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div class="stat bg-base-100 rounded-lg shadow">
          <div class="stat-figure text-primary">
            <span class="text-2xl">📄</span>
          </div>
          <div class="stat-title">文章</div>
          <div class="stat-value text-primary">{{ stats.posts || 0 }}</div>
        </div>

        <div class="stat bg-base-100 rounded-lg shadow">
          <div class="stat-figure text-secondary">
            <span class="text-2xl">📝</span>
          </div>
          <div class="stat-title">笔记</div>
          <div class="stat-value text-secondary">{{ stats.notes || 0 }}</div>
        </div>

        <div class="stat bg-base-100 rounded-lg shadow">
          <div class="stat-figure text-accent">
            <span class="text-2xl">💬</span>
          </div>
          <div class="stat-title">说说</div>
          <div class="stat-value text-accent">{{ stats.says || 0 }}</div>
        </div>

        <div class="stat bg-base-100 rounded-lg shadow">
          <div class="stat-figure text-info">
            <span class="text-2xl">👁️</span>
          </div>
          <div class="stat-title">访问量</div>
          <div class="stat-value text-info">{{ formatNumber(stats.views || 0) }}</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Mock stats data - replace with real API call
const stats = ref({
  posts: 42,
  notes: 128,
  says: 256,
  views: 12345
})

// Format large numbers
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// In a real app, you would fetch this data:
// const { data: statsData } = await useFetch('/api/stats')
// const stats = computed(() => statsData.value || {})
</script>
