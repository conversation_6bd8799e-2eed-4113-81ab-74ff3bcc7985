import * as React from 'react'

export function EmojiSmile() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M11 15a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm10 0a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm-9.2 3.4a1 1 0 1 0-1.6 1.2c.69.92 2.688 2.4 5.8 2.4c3.112 0 5.11-1.48 5.8-2.4a1 1 0 0 0-1.6-1.2c-.31.413-1.712 1.6-4.2 1.6c-2.488 0-3.89-1.188-4.2-1.6Z" />
        <path d="M4.763 5.423C7.313 2.654 11.095 1 16 1c4.904 0 8.686 1.654 11.235 4.423c2.537 2.755 3.764 6.515 3.764 10.576c0 4.061-1.227 7.82-3.764 10.576c-2.55 2.769-6.331 4.423-11.235 4.423c-4.904 0-8.686-1.654-11.236-4.423C2.226 23.82 1 20.06 1 15.999c0-4.061 1.226-7.821 3.763-10.576Zm1.472 1.355C4.105 9.089 3 12.328 3 15.998c0 3.671 1.106 6.91 3.235 9.222c2.116 2.298 5.333 3.778 9.764 3.778c4.43 0 7.648-1.48 9.764-3.778c2.129-2.311 3.235-5.55 3.235-9.221c0-3.67-1.106-6.91-3.235-9.221C23.647 4.48 20.43 3 16 3S8.352 4.48 6.236 6.778Z" />
      </g>
    </svg>
  )
}

export function EmojiSadCry() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M9.07 9.988c-.594.563-.952 1.24-1.096 1.67a.5.5 0 1 1-.948-.316c.19-.57.631-1.392 1.355-2.08C9.113 8.567 10.148 8 11.5 8a.5.5 0 1 1 0 1c-1.048 0-1.846.433-2.43.988ZM14 15a3 3 0 1 1-6 0a3 3 0 0 1 6 0Zm-4 10a1.99 1.99 0 0 1-.619 1.446A2 2 0 0 1 6 25v-3a2 2 0 1 1 4 0v3Zm11-7a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm-8.707 4.293a1 1 0 0 0 1.404 1.424l.008-.007a2.7 2.7 0 0 1 .492-.315c.378-.19.976-.395 1.803-.395s1.425.206 1.803.394a2.7 2.7 0 0 1 .492.316l.008.007a1 1 0 0 0 1.404-1.424l-.001-.002l-.002-.001l-.003-.003l-.008-.008l-.02-.02a3.347 3.347 0 0 0-.26-.215a4.69 4.69 0 0 0-.716-.438C18.075 21.294 17.173 21 16 21s-2.075.294-2.697.606a4.69 4.69 0 0 0-.716.438a3.347 3.347 0 0 0-.26.215l-.02.02l-.008.008l-.003.003l-.002.002h-.001ZM22.93 9.988c.594.563.952 1.24 1.096 1.67a.5.5 0 1 0 .948-.316c-.19-.57-.631-1.392-1.355-2.08C22.887 8.567 21.852 8 20.5 8a.5.5 0 1 0 0 1c1.048 0 1.846.433 2.43.988Z" />
        <path d="M4.763 5.423C7.313 2.654 11.095 1 16 1c4.904 0 8.686 1.654 11.235 4.423c2.537 2.755 3.764 6.515 3.764 10.576c0 4.061-1.227 7.82-3.764 10.576c-2.55 2.769-6.331 4.423-11.235 4.423c-4.904 0-8.686-1.654-11.236-4.423C2.226 23.82 1 20.06 1 15.999c0-4.061 1.226-7.821 3.763-10.576Zm1.472 1.355C4.105 9.089 3 12.328 3 15.998c0 2.879.68 5.492 2 7.6V25a3 3 0 0 0 4.463 2.62c1.78.875 3.952 1.378 6.536 1.378c4.43 0 7.648-1.48 9.764-3.778c2.129-2.311 3.235-5.55 3.235-9.221c0-3.67-1.106-6.91-3.235-9.221C23.647 4.48 20.43 3 16 3S8.352 4.48 6.236 6.778Z" />
      </g>
    </svg>
  )
}

export function EmojiSadTear() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M14.211 19a4.442 4.442 0 0 1-3.68 1.95c-.452 0-.889-.067-1.301-.192a.147.147 0 0 0-.19.138a1.104 1.104 0 0 1-2.209 0V20a1 1 0 0 1 1-1h6.38Zm3.539 0a4.442 4.442 0 0 0 3.68 1.95c.452 0 .89-.067 1.302-.192c.093-.029.19.04.19.138a1.104 1.104 0 0 0 2.208 0V20a1 1 0 0 0-1-1h-6.38ZM10.016 7.404l.003-.016a.5.5 0 0 1 .986.165l-.493-.082l.493.083v.002l-.001.004l-.002.01a1.753 1.753 0 0 1-.033.151a4.498 4.498 0 0 1-.745 1.552c-.65.866-1.795 1.702-3.718 1.702a.5.5 0 0 1 0-1c1.583 0 2.44-.667 2.917-1.302a3.496 3.496 0 0 0 .575-1.194l.018-.075Zm12.007 0l-.003-.016v-.001a.5.5 0 0 0-.986.166l.493-.082l-.493.083v.002l.001.004l.002.01a1.753 1.753 0 0 0 .034.151a4.494 4.494 0 0 0 .745 1.552c.65.866 1.794 1.702 3.717 1.702a.5.5 0 1 0 0-1c-1.583 0-2.44-.667-2.917-1.302a3.497 3.497 0 0 1-.575-1.194a1.791 1.791 0 0 1-.018-.075Zm-8.506 15.031a.75.75 0 0 0-1.003 1.115c.926.834 2.25 1.206 3.506 1.206c1.254 0 2.58-.372 3.506-1.206a.75.75 0 1 0-1.004-1.115c-.576.518-1.504.822-2.502.822c-.999 0-1.927-.304-2.503-.822Zm1.3-6.15c0 .619-.152 1.202-.42 1.715H7.834a3.701 3.701 0 1 1 6.982-1.715Zm-1.288.054c.546-.378.614-1.226.151-1.894c-.462-.669-1.28-.904-1.826-.527c-.546.378-.614 1.227-.151 1.895c.462.668 1.28.904 1.826.526ZM17.565 18h6.561a3.701 3.701 0 1 0-6.561 0Zm5.446-1.66c-.546.377-1.364.141-1.826-.527c-.463-.668-.395-1.517.151-1.895c.547-.377 1.364-.142 1.827.527c.462.668.394 1.516-.152 1.894Z" />
        <path d="M4.768 5.391C7.321 2.62 11.108.963 16.018.963s8.698 1.656 11.25 4.428c2.541 2.759 3.769 6.524 3.769 10.59c0 4.067-1.228 7.832-3.768 10.59C24.716 29.343 20.929 31 16.019 31S7.32 29.343 4.768 26.57C2.227 23.813 1 20.048 1 15.981c0-4.066 1.228-7.831 3.768-10.59ZM6.24 6.746C4.108 9.061 3 12.306 3 15.981c0 3.676 1.108 6.92 3.24 9.236C8.359 27.517 11.58 29 16.018 29c4.438 0 7.66-1.482 9.78-3.784c2.131-2.315 3.239-5.559 3.239-9.235c0-3.675-1.108-6.92-3.24-9.235c-2.12-2.301-5.341-3.783-9.779-3.783c-4.437 0-7.659 1.482-9.778 3.783Z" />
      </g>
    </svg>
  )
}

export function EmojiAngry() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M14.999 13.29a.75.75 0 0 0-.71-.789c-.828-.044-1.504-.241-2.039-.613c-.527-.367-.979-.948-1.289-1.876a.75.75 0 1 0-1.422.476c.394 1.18 1.019 2.049 1.854 2.63c.828.577 1.8.827 2.817.88a.75.75 0 0 0 .789-.708Zm2.002 0a.75.75 0 0 1 .709-.789c.83-.044 1.505-.241 2.04-.613c.526-.367.978-.948 1.289-1.876a.75.75 0 1 1 1.422.476c-.394 1.18-1.019 2.049-1.854 2.63c-.828.577-1.8.827-2.817.88a.75.75 0 0 1-.79-.708Zm-4.107 12.157C13.066 25.104 13.995 24 16 24s2.934 1.104 3.105 1.447a1 1 0 0 0 1.79-.894C20.398 23.563 18.794 22 16 22c-2.795 0-4.4 1.562-4.895 2.553a1 1 0 0 0 1.79.894ZM12 19a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm8 0a2 2 0 1 0 0-4a2 2 0 0 0 0 4Z" />
        <path d="M15.999 29.998c9.334 0 13.999-6.268 13.999-14c0-7.73-4.665-13.998-14-13.998C6.665 2 2 8.268 2 15.999c0 7.731 4.664 13.999 13.999 13.999Zm11.999-14c0 3.476-1.046 6.455-2.97 8.545c-1.9 2.063-4.835 3.455-9.03 3.455c-4.193 0-7.128-1.393-9.028-3.455C5.046 22.453 4 19.473 4 15.999c0-3.475 1.046-6.454 2.97-8.544C8.87 5.392 11.805 4 16 4s7.129 1.392 9.028 3.455c1.925 2.09 2.97 5.07 2.97 8.544Z" />
      </g>
    </svg>
  )
}

export function EmojiTired() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M13 5.5a.5.5 0 0 0-1 0c0 .864-.32 1.476-.77 1.876c-.46.409-1.087.624-1.73.624a.5.5 0 0 0 0 1c.857 0 1.73-.285 2.395-.876C12.569 7.524 13 6.636 13 5.5Zm6.5-.5a.5.5 0 0 0-.5.5c0 1.136.43 2.024 1.105 2.624c.666.591 1.538.876 2.395.876a.5.5 0 0 0 0-1c-.643 0-1.27-.215-1.73-.624c-.45-.4-.77-1.012-.77-1.876a.5.5 0 0 0-.5-.5Zm4 19c.829 0 1.513-.677 1.377-1.494a9.002 9.002 0 0 0-17.754 0C6.987 23.323 7.671 24 8.5 24h15ZM8.106 10.553a1 1 0 0 1 1.341-.448l4 2a1 1 0 0 1 0 1.79l-4 2a1 1 0 1 1-.894-1.79L10.763 13l-2.21-1.106a1 1 0 0 1-.447-1.341Zm14.447-.448a1 1 0 0 1 .894 1.79L21.237 13l2.21 1.105a1 1 0 0 1-.894 1.79l-4-2a1 1 0 0 1 0-1.79l4-2Z" />
        <path d="M15.999 1C11.095 1 7.313 2.654 4.763 5.423C2.226 8.178 1 11.938 1 15.999c0 4.061 1.226 7.82 3.763 10.576c2.55 2.769 6.332 4.423 11.236 4.423c4.904 0 8.686-1.654 11.235-4.423c2.537-2.755 3.764-6.515 3.764-10.576c0-4.061-1.227-7.821-3.764-10.576C24.684 2.654 20.903 1 16 1ZM3 15.999c0-3.67 1.106-6.91 3.235-9.221C8.35 4.48 11.568 3 15.999 3c4.43 0 7.648 1.48 9.764 3.778c2.129 2.311 3.235 5.55 3.235 9.22c0 3.671-1.106 6.91-3.235 9.222c-2.116 2.298-5.333 3.778-9.764 3.778c-4.43 0-7.648-1.48-9.764-3.778C4.105 22.91 3 19.67 3 16Z" />
      </g>
    </svg>
  )
}

export function EmojiMeh() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <path
        fill="currentColor"
        d="M8 22v-3h16v3H8Zm3-7a3 3 0 1 0 0-6a3 3 0 0 0 0 6Zm10 0a3 3 0 1 0 0-6a3 3 0 0 0 0 6Z"
      />
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M4.765 5.423C7.315 2.654 11.096 1 16 1c4.904 0 8.686 1.654 11.235 4.423C29.773 8.178 31 11.938 31 15.999c0 4.061-1.226 7.82-3.764 10.576c-2.55 2.769-6.331 4.423-11.235 4.423c-4.904 0-8.686-1.654-11.235-4.423C2.227 23.82 1 20.06 1 15.999c0-4.061 1.226-7.821 3.764-10.576Zm-1.726 9.41a17.66 17.66 0 0 0-.038 1.166c0 2.189.393 4.224 1.162 6.001H8v2h16v-2h3.837c.769-1.777 1.162-3.812 1.162-6.001c0-.394-.013-.783-.038-1.166L24 17.589V17H8v.589l-4.96-2.756Zm.283-2.131l5.593 3.107c.152-.092.318-.164.496-.212C11.073 15.153 13.876 14.5 16 14.5c2.124 0 4.927.653 6.589 1.097c.178.048.344.12.495.212l5.594-3.107c-.47-2.306-1.45-4.335-2.914-5.924C23.648 4.48 20.431 3 16 3c-4.43 0-7.648 1.48-9.764 3.778c-1.464 1.59-2.444 3.618-2.914 5.924ZM26.736 24h-2.759a1.868 1.868 0 0 1-1.181 1.484C21.22 26.076 18.379 27 16 27c-2.379 0-5.22-.924-6.796-1.516A1.868 1.868 0 0 1 8.023 24H5.264c.296.43.62.838.972 1.22c2.116 2.298 5.333 3.778 9.764 3.778c4.43 0 7.648-1.48 9.764-3.778c.352-.382.676-.79.972-1.22Z"
        clipRule="evenodd"
      />
    </svg>
  )
}

export function EmojiGrinSquintTears() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M6.235 6.778c-1.84 1.996-2.915 4.685-3.174 7.746l-2.053 2.053A19.92 19.92 0 0 1 1 16c0-4.061 1.226-7.821 3.763-10.576C7.313 2.654 11.095 1 16 1c4.904 0 8.686 1.654 11.235 4.423c2.537 2.755 3.764 6.515 3.764 10.576l-.003.332l-2.084-2.085c-.3-2.945-1.365-5.532-3.148-7.468C23.647 4.48 20.43 3 16 3S8.352 4.48 6.236 6.778ZM27.937 21.76a11.491 11.491 0 0 1-2.174 3.46c-2.116 2.298-5.333 3.778-9.764 3.778c-4.43 0-7.648-1.48-9.764-3.778a11.47 11.47 0 0 1-2.14-3.38a3.138 3.138 0 0 1-2.175-.092c.624 1.803 1.568 3.442 2.843 4.827c2.55 2.769 6.332 4.423 11.236 4.423c4.904 0 8.686-1.654 11.235-4.423c1.25-1.358 2.183-2.96 2.807-4.722a3.135 3.135 0 0 1-2.104-.094Z" />
        <path d="M22.669 16H9.58a3.107 3.107 0 0 1-.874 1.707l-1.21 1.211C8.302 21.547 10.424 25 16 25c5.712 0 7.8-3.626 8.561-6.274l-1.018-1.019A3.107 3.107 0 0 1 22.669 16ZM4.63 20.37L8 17a2.121 2.121 0 1 0-3-3l-3.37 3.37a2.121 2.121 0 0 0 3 3ZM24.25 17l3.37 3.37a2.121 2.121 0 1 0 3-3L27.25 14a2.121 2.121 0 0 0-3 3ZM9.349 10.412c-.21.228-.32.532-.367.779a1 1 0 0 1-1.964-.382c.089-.456.31-1.153.858-1.749C8.453 8.431 9.316 8 10.5 8c1.195 0 2.062.452 2.636 1.082c.544.597.77 1.292.85 1.747a1 1 0 0 1-1.971.342a1.546 1.546 0 0 0-.358-.742C11.47 10.222 11.142 10 10.5 10c-.653 0-.972.217-1.151.412Zm11.001 0c-.21.228-.32.532-.368.779a1 1 0 0 1-1.963-.382c.088-.456.31-1.153.857-1.749C19.453 8.431 20.316 8 21.5 8c1.196 0 2.062.452 2.636 1.082c.544.597.77 1.292.85 1.747a1 1 0 0 1-1.971.342a1.545 1.545 0 0 0-.358-.742C22.47 10.222 22.142 10 21.5 10c-.653 0-.971.217-1.15.412Z" />
      </g>
    </svg>
  )
}

export function EmojiFrownOpen() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M12 16a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm8 0a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm.901 8.006c.11.542-.35.994-.902.994H12c-.552 0-1.01-.452-.901-.994a5.002 5.002 0 0 1 9.802 0Z" />
        <path d="M15.999 1C11.095 1 7.313 2.654 4.763 5.423C2.226 8.178 1 11.938 1 15.999c0 4.061 1.226 7.82 3.763 10.576c2.55 2.769 6.332 4.423 11.236 4.423c4.904 0 8.686-1.654 11.235-4.423c2.537-2.755 3.764-6.515 3.764-10.576c0-4.061-1.227-7.821-3.764-10.576C24.684 2.654 20.903 1 16 1ZM3 15.999c0-3.67 1.106-6.91 3.235-9.221C8.35 4.48 11.568 3 15.999 3c4.43 0 7.648 1.48 9.764 3.778c2.129 2.311 3.235 5.55 3.235 9.22c0 3.671-1.106 6.91-3.235 9.222c-2.116 2.298-5.333 3.778-9.764 3.778c-4.43 0-7.648-1.48-9.764-3.778C4.105 22.91 3 19.67 3 16Z" />
      </g>
    </svg>
  )
}

export function EmojiGrimace() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M12 14a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm10-2a2 2 0 1 1-4 0a2 2 0 0 1 4 0ZM8.512 23.677A2.988 2.988 0 0 0 11 25h10a2.989 2.989 0 0 0 2.437-1.25A2.984 2.984 0 0 0 24 22c0-.699-.24-1.342-.64-1.852A2.995 2.995 0 0 0 21 19H11c-.906 0-1.718.401-2.268 1.036A2.988 2.988 0 0 0 8 22c0 .621.189 1.198.512 1.677ZM11 20h10a2 2 0 0 1 1.792 1.11a.998.998 0 0 1-.792.39H10a.998.998 0 0 1-.792-.39A2 2 0 0 1 11 20Zm11.792 2.89A2 2 0 0 1 21 24H11a2 2 0 0 1-1.792-1.11a.998.998 0 0 1 .792-.39h12c.322 0 .609.152.792.39Z" />
        <path d="M15.999 1C11.095 1 7.313 2.654 4.763 5.423C2.226 8.178 1 11.938 1 15.999c0 4.061 1.226 7.82 3.763 10.576c2.55 2.769 6.332 4.423 11.236 4.423c4.904 0 8.686-1.654 11.235-4.423c2.537-2.755 3.764-6.515 3.764-10.576c0-4.061-1.227-7.821-3.764-10.576C24.684 2.654 20.903 1 16 1ZM3 15.999c0-3.67 1.106-6.91 3.235-9.221C8.35 4.48 11.568 3 15.999 3c4.43 0 7.648 1.48 9.764 3.778c2.129 2.311 3.235 5.55 3.235 9.22c0 3.671-1.106 6.91-3.235 9.222c-2.116 2.298-5.333 3.778-9.764 3.778c-4.43 0-7.648-1.48-9.764-3.778C4.105 22.91 3 19.67 3 16Z" />
      </g>
    </svg>
  )
}

export function EmojiFlushed() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
    >
      <g fill="currentColor">
        <path d="M9.07 8.988c-.594.562-.952 1.24-1.096 1.67a.5.5 0 1 1-.948-.316c.19-.57.631-1.392 1.355-2.08C9.113 7.567 10.148 7 11.5 7a.5.5 0 1 1 0 1c-1.048 0-1.846.433-2.43.988ZM14 14a2 2 0 1 1-4 0a2 2 0 0 1 4 0Zm-5 7c1.657 0 3-.895 3-2s-1.343-2-3-2s-3 .895-3 2s1.343 2 3 2Zm17-2c0 1.105-1.343 2-3 2s-3-.895-3-2s1.343-2 3-2s3 .895 3 2Zm-6-3a2 2 0 1 0 0-4a2 2 0 0 0 0 4Zm2.93-7.012c.594.562.952 1.24 1.096 1.67a.5.5 0 1 0 .948-.316c-.19-.57-.631-1.392-1.355-2.08C22.887 7.567 21.852 7 20.5 7a.5.5 0 1 0 0 1c1.048 0 1.846.433 2.43.988ZM13 23a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2h-4a1 1 0 0 1-1-1Z" />
        <path d="M15.999 1C11.095 1 7.313 2.654 4.763 5.423C2.226 8.178 1 11.938 1 15.999c0 4.061 1.226 7.82 3.763 10.576c2.55 2.769 6.332 4.423 11.236 4.423c4.904 0 8.686-1.654 11.235-4.423c2.537-2.755 3.764-6.515 3.764-10.576c0-4.061-1.227-7.821-3.764-10.576C24.684 2.654 20.903 1 16 1ZM3 15.999c0-3.67 1.106-6.91 3.235-9.221C8.35 4.48 11.568 3 15.999 3c4.43 0 7.648 1.48 9.764 3.778c2.129 2.311 3.235 5.55 3.235 9.22c0 3.671-1.106 6.91-3.235 9.222c-2.116 2.298-5.333 3.778-9.764 3.778c-4.43 0-7.648-1.48-9.764-3.778C4.105 22.91 3 19.67 3 16Z" />
      </g>
    </svg>
  )
}
