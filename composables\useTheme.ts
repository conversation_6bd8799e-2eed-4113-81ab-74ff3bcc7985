/**
 * Theme management composable
 */
export const useTheme = () => {
  const colorMode = useColorMode()
  
  const isDark = computed(() => colorMode.value === 'dark')
  const isLight = computed(() => colorMode.value === 'light')
  
  const toggleTheme = () => {
    colorMode.preference = isDark.value ? 'light' : 'dark'
  }
  
  const setTheme = (theme: 'light' | 'dark' | 'system') => {
    colorMode.preference = theme
  }
  
  return {
    colorMode: readonly(colorMode),
    isDark: readonly(isDark),
    isLight: readonly(isLight),
    toggleTheme,
    setTheme
  }
}

/**
 * Responsive design composable
 */
export const useBreakpoints = () => {
  const { width } = useWindowSize()
  
  const isMobile = computed(() => width.value < 768)
  const isTablet = computed(() => width.value >= 768 && width.value < 1024)
  const isDesktop = computed(() => width.value >= 1024)
  const isLarge = computed(() => width.value >= 1280)
  const isXLarge = computed(() => width.value >= 1536)
  
  return {
    width: readonly(width),
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),
    isLarge: readonly(isLarge),
    isXLarge: readonly(isXLarge)
  }
}

/**
 * Local storage composable with SSR support
 */
export const useLocalStorage = <T>(
  key: string,
  defaultValue: T,
  options?: {
    serializer?: {
      read: (value: string) => T
      write: (value: T) => string
    }
  }
) => {
  const serializer = options?.serializer || {
    read: JSON.parse,
    write: JSON.stringify
  }
  
  const storedValue = process.client 
    ? localStorage.getItem(key)
    : null
  
  const initialValue = storedValue !== null 
    ? serializer.read(storedValue)
    : defaultValue
  
  const state = ref<T>(initialValue)
  
  const setValue = (value: T | ((prev: T) => T)) => {
    const newValue = typeof value === 'function' 
      ? (value as (prev: T) => T)(state.value)
      : value
    
    state.value = newValue
    
    if (process.client) {
      localStorage.setItem(key, serializer.write(newValue))
    }
  }
  
  const removeValue = () => {
    state.value = defaultValue
    if (process.client) {
      localStorage.removeItem(key)
    }
  }
  
  return [
    readonly(state),
    setValue,
    removeValue
  ] as const
}
